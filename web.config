<?xml version="1.0" encoding="utf-8"?>
<configuration>
  <system.webServer>
    <!-- First we consider whether the incoming URL matches a physical file in the /dist folder -->
    <rewrite>
      <rules>
        <!-- Handle Socket.IO requests -->
        <rule name="SocketIO" stopProcessing="true">
          <match url="^socket\.io/.*" />
          <action type="Rewrite" url="server/index.js" />
        </rule>
        
        <!-- Handle API requests -->
        <rule name="API" stopProcessing="true">
          <match url="^api/.*" />
          <action type="Rewrite" url="server/index.js" />
        </rule>
        
        <!-- Handle debug requests -->
        <rule name="Debug" stopProcessing="true">
          <match url="^debug/.*" />
          <action type="Rewrite" url="server/index.js" />
        </rule>
        
        <!-- Handle iisnode requests -->
        <rule name="iisnode" stopProcessing="true">
          <match url="^iisnode/.*" />
          <action type="Rewrite" url="server/index.js" />
        </rule>
        
        <!-- Static files from dist folder -->
        <rule name="StaticContent" stopProcessing="true">
          <match url="^(assets|CardFace|SuitFaces|sounds)/.*" />
          <action type="Rewrite" url="dist/{R:0}" />
        </rule>
        
        <!-- Handle all other requests with React Router -->
        <rule name="ReactRouter" stopProcessing="true">
          <match url=".*" />
          <conditions logicalGrouping="MatchAll">
            <add input="{REQUEST_FILENAME}" matchType="IsFile" negate="true" />
            <add input="{REQUEST_FILENAME}" matchType="IsDirectory" negate="true" />
            <add input="{REQUEST_URI}" pattern="^/(socket\.io|api|debug|iisnode)" negate="true" />
          </conditions>
          <action type="Rewrite" url="dist/index.html" />
        </rule>
      </rules>
    </rewrite>
    
    <!-- Configure IISNode for Node.js -->
    <handlers>
      <add name="iisnode" path="server/index.js" verb="*" modules="iisnode" />
    </handlers>
    
    <!-- IISNode configuration -->
    <iisnode 
      node_env="production"
      nodeProcessCommandLine="C:\Program Files\nodejs\node.exe"
      interceptor="server/index.js"
      enableXFF="true"
      promoteServerVars="LOGON_USER,AUTH_USER,AUTH_TYPE"
      configOverrides="server/iisnode.yml"
      watchedFiles="*.js;node_modules\*;server\*.js;server\handlers\*.js;server\utils\*.js"
      loggingEnabled="true"
      logDirectory="iisnode"
      debuggingEnabled="false"
      devErrorsEnabled="false"
      flushResponse="false"
      maxNamedPipeConnectionRetry="100"
      namedPipeConnectionRetryDelay="250"
      maxNamedPipeConnectionPoolSize="512"
      maxNamedPipePooledConnectionAge="30000"
      asyncCompletionThreadCount="0"
      maxConcurrentRequestsPerProcess="1024"
      maxProcessCountPerApplication="1"
      nodeProcessCountPerApplication="1"
    />
    
    <!-- Security settings -->
    <security>
      <requestFiltering>
        <hiddenSegments>
          <add segment="node_modules" />
          <add segment="server" />
          <add segment="src" />
        </hiddenSegments>
      </requestFiltering>
    </security>
    
    <!-- Default document -->
    <defaultDocument>
      <files>
        <clear />
        <add value="dist/index.html" />
      </files>
    </defaultDocument>
    
    <!-- Static content settings -->
    <staticContent>
      <mimeMap fileExtension=".svg" mimeType="image/svg+xml" />
      <mimeMap fileExtension=".json" mimeType="application/json" />
      <mimeMap fileExtension=".js" mimeType="application/javascript" />
      <mimeMap fileExtension=".css" mimeType="text/css" />
    </staticContent>
    
    <!-- HTTP errors -->
    <httpErrors errorMode="Detailed" />
    
    <!-- Compression -->
    <urlCompression doStaticCompression="true" doDynamicCompression="true" />
    
    <!-- WebSocket support -->
    <webSocket enabled="true" />
    
  </system.webServer>
</configuration>
