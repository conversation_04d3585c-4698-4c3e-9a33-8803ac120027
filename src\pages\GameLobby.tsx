"use client";
import { useState, useEffect } from "react";
import { <PERSON><PERSON>, Share2, AlertCircle } from "lucide-react";
import { Input } from "@/components/ui/input";
// Use public path for GRCard image
const GRCard = "/assets/GRCard.png";
import LobbyButton from "@/components/LobbyButton";
import { useNavigate } from "react-router-dom";
import BurgerMenu from "@/components/BurgerMenu";
import { Button } from "@/components/ui/button";
import { useLobbyStore } from "@/store/lobbyStore";
import { Alert, AlertDescription } from "@/components/ui/alert";
import socketService from "@/services/socketService";
import InviteCodes from "@/components/InviteCodes";

export default function GameLobby() {
  const [isLoading, setIsLoading] = useState(false);
  const [errorMessage, setErrorMessage] = useState<string | null>(null);
  const [forceUpdate, setForceUpdate] = useState(0); // Used to force component updates
  const [isEditingTeamName, setIsEditingTeamName] = useState(false);
  const [editedTeamName, setEditedTeamName] = useState('');
  const navigate = useNavigate();

  // Listen for storage events (used as a hack to force re-renders)
  useEffect(() => {
    const handleStorageEvent = () => {
      console.log('Storage event detected, forcing update');
      setForceUpdate(prev => prev + 1);
    };

    window.addEventListener('storage', handleStorageEvent);

    return () => {
      window.removeEventListener('storage', handleStorageEvent);
    };
  }, []);

  // Get state and actions from the lobby store
  const {
    lobbyCode,
    players,
    isHost,
    startGame,
    setTeamReady,
    switchTeam,
    teamReady,
    teamNames,
    error,
    isGameStarted,
    isFindingMatch,
    matchedLobby,
    matchedTeam,
    matchedTeamName,
    findMatch,
    cancelFindMatch,
    updateTeamName
  } = useLobbyStore();

  // Redirect if no lobby code
  useEffect(() => {
    if (!lobbyCode) {
      navigate('/');
    }
  }, [lobbyCode, navigate]);

  // Redirect to game if game started
  useEffect(() => {
    if (isGameStarted) {
      navigate('/game');
    }
  }, [isGameStarted, navigate]);

  // Show error from the store
  useEffect(() => {
    if (error) {
      setErrorMessage(error);
      setIsLoading(false);
    }
  }, [error]);

  // Get current player from players array
  const currentPlayer = players.find(p => p.id === socketService.getSocketId());
  // Ensure currentTeam is typed as 1 or 2
  const currentTeam = (currentPlayer?.team as 1 | 2) || 1;

  // Function to handle team readiness
  const handleTeamReady = async () => {
    setIsLoading(true);
    setErrorMessage(null);

    try {
      // Toggle team ready status
      await setTeamReady(!teamReady[currentTeam]);
      setIsLoading(false);
    } catch (error) {
      setErrorMessage(error instanceof Error ? error.message : 'Failed to set team ready status');
      setIsLoading(false);
    }
  };

  // Function to handle team switching
  const handleSwitchTeam = async () => {
    setIsLoading(true);
    setErrorMessage(null);

    try {
      await switchTeam();
      setIsLoading(false);
    } catch (error) {
      setErrorMessage(error instanceof Error ? error.message : 'Failed to switch team');
      setIsLoading(false);
    }
  };

  // Function to handle team name editing
  const handleEditTeamName = () => {
    // Initialize with current team name
    setEditedTeamName(teamNames[1]);
    setIsEditingTeamName(true);
  };

  // Function to save team name
  const handleSaveTeamName = async () => {
    if (!editedTeamName.trim()) {
      setErrorMessage("Team name cannot be empty");
      return;
    }

    setIsLoading(true);
    setErrorMessage(null);

    try {
      await updateTeamName(1, editedTeamName.trim());
      setIsEditingTeamName(false);
      setIsLoading(false);
    } catch (error) {
      setErrorMessage(error instanceof Error ? error.message : 'Failed to update team name');
      setIsLoading(false);
    }
  };

  // Function to cancel team name editing
  const handleCancelEditTeamName = () => {
    setIsEditingTeamName(false);
    setEditedTeamName('');
  };

  // Function to handle finding a match
  const handleFindMatch = async () => {
    // Check if team 1 has 2 players and is ready
    const team1Players = players.filter(p => p.team === 1);

    if (team1Players.length !== 2) {
      setErrorMessage("Team 1 must have exactly 2 players");
      return;
    }

    if (!teamReady[1]) {
      setErrorMessage("Team 1 must be ready to find a match");
      return;
    }

    setIsLoading(true);
    setErrorMessage(null);

    try {
      console.log('Finding match for team:', team1Players);
      await findMatch();
      console.log('Find match request sent successfully');
      setIsLoading(false);
    } catch (error) {
      console.error('Error finding match:', error);
      setErrorMessage(error instanceof Error ? error.message : 'Failed to find match');
      setIsLoading(false);
    }
  };

  // Function to handle canceling match finding
  const handleCancelFindMatch = async () => {
    setIsLoading(true);
    setErrorMessage(null);

    try {
      await cancelFindMatch();
      setIsLoading(false);
    } catch (error) {
      setErrorMessage(error instanceof Error ? error.message : 'Failed to cancel match finding');
      setIsLoading(false);
    }
  };

  // Function to handle starting the game
  const handleStartGame = async () => {
    // Check if matched with another team
    if (!matchedLobby || !matchedTeam) {
      setErrorMessage("Not matched with another team yet. Use Find Match first.");
      return;
    }

    // Check if team 1 has 2 players
    const team1Players = players.filter(p => p.team === 1);

    if (team1Players.length !== 2) {
      setErrorMessage("Team 1 must have exactly 2 players");
      return;
    }

    setIsLoading(true);
    setErrorMessage(null);

    try {
      await startGame();
      setIsLoading(false);
    } catch (error) {
      setErrorMessage(error instanceof Error ? error.message : 'Failed to start game');
      setIsLoading(false);
    }
  };

  // Function to copy lobby code to clipboard
  const copyLobbyCode = () => {
    if (lobbyCode) {
      navigator.clipboard.writeText(lobbyCode);
    }
  };

  // Function to share lobby code
  const shareLobbyCode = () => {
    if (lobbyCode) {
      const shareText = `Join my Thunee game with code: ${lobbyCode}`;
      if (navigator.share) {
        navigator.share({
          title: 'Join my Thunee game',
          text: shareText,
        }).catch(console.error);
      } else {
        navigator.clipboard.writeText(shareText);
      }
    }
  };

  // SVG components for buttons
  const HostSVG = () => (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="none"
      stroke="currentColor"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
    >
      <path d="M12 2v20" />
      <path d="M2 12h20" />
    </svg>
  );

  const ReadySVG = () => (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="none"
      stroke="currentColor"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
    >
      <path d="M20 6 9 17l-5-5" />
    </svg>
  );

  const SwitchSVG = () => (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="none"
      stroke="currentColor"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
    >
      <path d="m2 9 3-3 3 3" />
      <path d="M13 18H7a2 2 0 0 1-2-2V6" />
      <path d="m22 15-3 3-3-3" />
      <path d="M11 6h6a2 2 0 0 1 2 2v10" />
    </svg>
  );

  return (
    <div className="flex flex-col h-screen bg-black text-white">
      {/* Header with burger menu */}
      <div className="flex justify-between items-center p-4 border-b border-[#E1C760]/50">
        <BurgerMenu />
        <div className="flex-1 flex justify-center">
          <img src={GRCard} alt="GR Card" className="h-10" />
        </div>
        <div className="w-8"></div> {/* Empty div for balance */}
      </div>

      {/* Main content */}
      <div className="flex-1 overflow-y-auto p-4">
        <div className="max-w-md mx-auto space-y-6">
          {/* Title */}
          {/* <div className="w-full text-center">
            <div className="w-full border-t border-b border-[#E1C760]/50 py-2">
              <div className="w-full h-px bg-[#E1C760]/50 mb-2"></div>
              <h1 className="text-3xl font-bold text-[#E1C760]">Thunee</h1>
              <div className="w-full h-px bg-[#E1C760]/50 mt-2"></div>
            </div>
          </div> */}

          {/* Host Title */}
          {/* <div className="w-full text-center">
            <h2 className="text-2xl font-bold text-[#E1C760]">Host</h2>
          </div> */}

          {/* Find Match Button - Only visible when team 1 is ready */}
          {teamReady[1] && !isFindingMatch && !matchedLobby && (
            <div className="w-full">
              <LobbyButton
                svgComponent={<HostSVG />}
                buttonText={isLoading ? "Finding..." : "Find Match"}
                onClick={handleFindMatch}
                centered={true}
                disabled={isLoading}
              />
            </div>
          )}

          {/* Cancel Find Match Button - Only visible when finding a match */}
          {isFindingMatch && (
            <div className="w-full">
              <LobbyButton
                svgComponent={<HostSVG />}
                buttonText={isLoading ? "Canceling..." : "Cancel Search"}
                onClick={handleCancelFindMatch}
                centered={true}
                disabled={isLoading}
              />
            </div>
          )}

          {/* Start Game Button - Only visible to host when matched with another team */}
          {isHost && matchedLobby && matchedTeam && (
            <div className="w-full">
              <LobbyButton
                svgComponent={<HostSVG />}
                buttonText={isLoading ? "Starting..." : "Start Game"}
                onClick={handleStartGame}
                centered={true}
                disabled={isLoading}
              />
            </div>
          )}

          {/* Team Ready Status */}
          {!teamReady[1] && (
            <div className="text-center text-sm text-[#E1C760]">
              Team 1 is not ready. Both players must be ready to find a match.
            </div>
          )}

          {/* Finding Match Status */}
          {isFindingMatch && (
            <div className="text-center text-sm text-[#E1C760]">
              Looking for another team to play against...
              <div className="mt-2 text-xs">Lobby Code: {lobbyCode}</div>
              <div className="mt-2">
                <Button
                  variant="outline"
                  size="sm"
                  className="border-[#E1C760] text-[#E1C760] hover:bg-[#E1C760]/10"
                  onClick={() => window.location.reload()}
                >
                  Refresh Status
                </Button>
              </div>
            </div>
          )}

          {/* Matched Team Status */}
          {matchedLobby && matchedTeam && (
            <div className="text-center text-sm text-[#E1C760]">
              Match found! Ready to start the game.
              <div className="mt-1">Matched with team from lobby: {matchedLobby}</div>
              {!isHost && <div className="mt-1">Waiting for host to start the game...</div>}
              <div className="mt-2">
                <Button
                  variant="outline"
                  size="sm"
                  className="border-[#E1C760] text-[#E1C760] hover:bg-[#E1C760]/10"
                  onClick={() => window.location.reload()}
                >
                  Refresh Status
                </Button>
              </div>
            </div>
          )}

          {errorMessage && (
            <Alert variant="destructive" className="mt-2 bg-red-900/50 border border-red-500">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>{errorMessage}</AlertDescription>
            </Alert>
          )}

          {/* Team Management */}
          <div className="w-full">
            <div className="flex justify-between items-center mb-4">
              <h3 className="text-xl font-semibold text-[#E1C760]">Teams</h3>
              <div className="flex space-x-2">
                <Button
                  variant="outline"
                  size="sm"
                  className="border-[#E1C760] text-[#E1C760] hover:bg-[#E1C760]/10"
                  onClick={handleSwitchTeam}
                >
                  <SwitchSVG />
                  <span className="ml-2">Switch Team</span>
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  className={`border-[#E1C760] ${
                    teamReady[currentTeam]
                      ? "bg-green-900/20 text-green-500 border-green-500"
                      : "text-[#E1C760] hover:bg-[#E1C760]/10"
                  }`}
                  onClick={handleTeamReady}
                >
                  <ReadySVG />
                  <span className="ml-2">{teamReady[currentTeam] ? "Ready ✓" : "Ready"}</span>
                </Button>
              </div>
            </div>

            {/* Team Containers */}
            <div className="space-y-4">
              {/* Team 1 */}
              <div className="border border-[#E1C760]/50 rounded-lg p-3">
                <div className="flex justify-between items-center mb-2">
                  {isEditingTeamName ? (
                    <div className="flex items-center space-x-2">
                      <Input
                        value={editedTeamName}
                        onChange={(e) => setEditedTeamName(e.target.value)}
                        className="bg-transparent border border-[#E1C760] text-white w-40"
                        placeholder="Enter team name"
                        maxLength={20}
                      />
                      <Button
                        size="sm"
                        variant="outline"
                        className="border-green-500 text-green-500 hover:bg-green-900/20"
                        onClick={handleSaveTeamName}
                        disabled={isLoading}
                      >
                        Save
                      </Button>
                      <Button
                        size="sm"
                        variant="outline"
                        className="border-red-500 text-red-500 hover:bg-red-900/20"
                        onClick={handleCancelEditTeamName}
                      >
                        Cancel
                      </Button>
                    </div>
                  ) : (
                    <div className="flex items-center">
                      <h4 className="text-[#E1C760] font-medium">{teamNames[1]} {teamReady[1] && '(Ready ✓)'}</h4>
                      {isHost && (
                        <Button
                          size="sm"
                          variant="ghost"
                          className="ml-2 text-[#E1C760] hover:bg-[#E1C760]/10 p-1 h-6"
                          onClick={handleEditTeamName}
                        >
                          ✏️
                        </Button>
                      )}
                    </div>
                  )}
                </div>
                <div className="grid grid-cols-2 gap-4">
                  {/* Team 1 Players */}
                  {Array(2).fill(0).map((_, index) => {
                    // Get team 1 players
                    const team1Players = players.filter(p => p.team === 1);
                    const player = team1Players[index] || null;

                    return (
                      <div key={`team1-${index}`} className="flex flex-col items-center">
                        <div
                          className={`w-16 h-16 rounded-full overflow-hidden border-2 ${
                            player ? "border-[#E1C760]" : "border-gray-600"
                          } flex items-center justify-center`}
                        >
                          {player ? (
                            <img
                              src={player.avatar}
                              alt={player.name}
                              className="w-full h-full object-cover"
                            />
                          ) : (
                            <div className="w-full h-full bg-gray-800 flex items-center justify-center text-gray-500">
                              <span>?</span>
                            </div>
                          )}
                        </div>
                        <span className="mt-2 text-sm text-center">
                          {player ? player.name : "Empty"}
                        </span>
                        {player?.isHost && (
                          <span className="text-xs text-[#E1C760] mt-1">Host</span>
                        )}
                        {player?.isReady && (
                          <span className="text-xs text-green-500 mt-1">Ready</span>
                        )}
                      </div>
                    );
                  })}
                </div>
              </div>

              {/* Team 2 */}
              <div className="border border-[#E1C760]/50 rounded-lg p-3">
                <div className="flex justify-between items-center mb-2">
                  <h4 className="text-[#E1C760] font-medium">
                    {matchedTeam ? (
                      <span>{matchedTeamName || 'Team 2'} (Matched Team)</span>
                    ) : (
                      <span>{teamNames[2]}</span>
                    )}
                  </h4>
                </div>
                <div className="grid grid-cols-2 gap-4">
                  {/* If we have a matched team, show them */}
                  {matchedTeam ? (
                    matchedTeam.map((player, index) => (
                      <div key={`matched-${index}`} className="flex flex-col items-center">
                        <div className="w-16 h-16 rounded-full overflow-hidden border-2 border-[#E1C760] flex items-center justify-center">
                          <img
                            src={player.avatar}
                            alt={player.name}
                            className="w-full h-full object-cover"
                          />
                        </div>
                        <span className="mt-2 text-sm text-center">{player.name}</span>
                        {player.isHost && (
                          <span className="text-xs text-[#E1C760] mt-1">Host</span>
                        )}
                        <span className="text-xs text-green-500 mt-1">Ready</span>
                      </div>
                    ))
                  ) : (
                    // Otherwise show empty slots or local team 2 players
                    Array(2).fill(0).map((_, index) => {
                      // Get team 2 players
                      const team2Players = players.filter(p => p.team === 2);
                      const player = team2Players[index] || null;

                      return (
                        <div key={`team2-${index}`} className="flex flex-col items-center">
                          <div
                            className={`w-16 h-16 rounded-full overflow-hidden border-2 ${
                              player ? "border-[#E1C760]" : "border-gray-600"
                            } flex items-center justify-center`}
                          >
                            {player ? (
                              <img
                                src={player.avatar}
                                alt={player.name}
                                className="w-full h-full object-cover"
                              />
                            ) : (
                              <div className="w-full h-full bg-gray-800 flex items-center justify-center text-gray-500">
                                <span>?</span>
                              </div>
                            )}
                          </div>
                          <span className="mt-2 text-sm text-center">
                            {player ? player.name : "Empty"}
                          </span>
                          {player?.isHost && (
                            <span className="text-xs text-[#E1C760] mt-1">Host</span>
                          )}
                          {player?.isReady && (
                            <span className="text-xs text-green-500 mt-1">Ready</span>
                          )}
                        </div>
                      );
                    })
                  )}
                </div>
              </div>
            </div>
          </div>

          {/* Lobby Code and Share Buttons */}
          <div className="w-full">
            <div className="flex items-center space-x-2">
              <Input
                value={lobbyCode || ''}
                readOnly
                className="bg-gray-800 border-[#E1C760]/50 text-center"
              />
              <Button
                variant="outline"
                size="icon"
                className="border-[#E1C760] text-[#E1C760] hover:bg-[#E1C760]/10"
                onClick={copyLobbyCode}
              >
                <Copy className="h-4 w-4" />
              </Button>
              <Button
                variant="outline"
                size="icon"
                className="border-[#E1C760] text-[#E1C760] hover:bg-[#E1C760]/10"
                onClick={shareLobbyCode}
              >
                <Share2 className="h-4 w-4" />
              </Button>
            </div>

            {/* Invite Codes Component */}
            <InviteCodes />

            {/* Debug buttons */}
            {isFindingMatch && (
              <div className="mt-4 space-y-2">
                <Button
                  variant="outline"
                  size="sm"
                  className="w-full border-red-500 text-red-500 hover:bg-red-500/10"
                  onClick={() => {
                    // Get another lobby code from the user
                    const otherLobbyCode = prompt('Enter the other lobby code to force match with:');
                    if (otherLobbyCode && lobbyCode) {
                      // Call the debug endpoint to force a match
                      fetch(`/debug/force-match?lobby1=${lobbyCode}&lobby2=${otherLobbyCode}`)
                        .then(response => response.json())
                        .then(data => {
                          console.log('Force match response:', data);
                          if (data.success) {
                            alert('Match forced successfully!');
                          } else {
                            alert(`Failed to force match: ${data.error}`);
                          }
                        })
                        .catch(err => {
                          console.error('Error forcing match:', err);
                          alert('Error forcing match. See console for details.');
                        });
                    }
                  }}
                >
                  Debug: Force Match
                </Button>

                <Button
                  variant="outline"
                  size="sm"
                  className="w-full border-blue-500 text-blue-500 hover:bg-blue-500/10"
                  onClick={() => {
                    // Call the debug endpoint to check the match queue
                    fetch('/debug/match-queue')
                      .then(response => response.json())
                      .then(data => {
                        console.log('Match queue:', data);
                        alert(`Match Queue: ${JSON.stringify(data, null, 2)}`);
                      })
                      .catch(err => {
                        console.error('Error checking match queue:', err);
                        alert('Error checking match queue. See console for details.');
                      });
                  }}
                >
                  Debug: Check Queue
                </Button>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
