"use client";
import { useState, useEffect } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { useGameStore } from "@/store/gameStore";
import socketService from "@/services/socketService";

interface CardDealingAnimationProps {
  isVisible: boolean;
  onComplete: () => void;
  dealingPhase: "dealFour" | "dealTwo";
  dealSpeed?: number; // Speed of dealing in milliseconds
}

export default function CardDealingAnimation({
  isVisible,
  onComplete,
  dealingPhase,
  dealSpeed = 300,
}: CardDealingAnimationProps) {
  const { players } = useGameStore();
  const [cardsDealt, setCardsDealt] = useState(0);
  const [currentPlayerIndex, setCurrentPlayerIndex] = useState(0);
  const [isAnimating, setIsAnimating] = useState(false);
  const [message, setMessage] = useState("");
  const [dealingComplete, setDealingComplete] = useState(false);

  // Total cards to deal based on phase
  const totalCardsToDeal = dealingPhase === "dealFour" ? 16 : 8; // 4 or 2 cards per player

  // Reset state when component becomes visible
  useEffect(() => {
    if (isVisible) {
      setCardsDealt(0);
      setCurrentPlayerIndex(0);
      setIsAnimating(false);
      setDealingComplete(false);
      setMessage(dealingPhase === "dealFour" ?
        "Dealing first 4 cards to each player..." :
        "Dealing final 2 cards to each player...");

      // Start the dealing animation immediately
      setIsAnimating(true);
    }
  }, [isVisible, dealingPhase]);

  // Handle the dealing animation
  useEffect(() => {
    if (!isVisible || !isAnimating || dealingComplete) return;

    // Function to deal a single card
    const dealCard = () => {
      // Calculate which player gets the card
      const playerIndex = cardsDealt % 4;
      setCurrentPlayerIndex(playerIndex);

      // Increment cards dealt counter
      setCardsDealt(prev => prev + 1);

      // Check if we've dealt all cards
      if (cardsDealt + 1 >= totalCardsToDeal) {
        setDealingComplete(true);
        setMessage(dealingPhase === "dealFour" ?
          "First 4 cards dealt to each player!" :
          "All cards dealt!");

        // Call onComplete after a delay
        setTimeout(() => {
          onComplete();
        }, 1500);
      }
    };

    // Set up interval to deal cards
    const interval = setInterval(dealCard, dealSpeed);

    return () => clearInterval(interval);
  }, [isVisible, isAnimating, dealingComplete, cardsDealt, currentPlayerIndex, dealingPhase, dealSpeed, totalCardsToDeal, onComplete]);

  // If not visible, don't render anything
  if (!isVisible) {
    return null;
  }

  // Get player names
  const getPlayerName = (index: number) => {
    // If we have real players, use their names
    if (players.length === 4) {
      // Sort players by ID to ensure consistent order
      const sortedPlayers = [...players].sort((a, b) => a.id.localeCompare(b.id));
      return sortedPlayers[index]?.name || `Player ${index + 1}`;
    }

    // Default names
    return ["Top Player", "Right Player", "Bottom Player", "Left Player"][index];
  };

  // Calculate cards dealt to each player
  const getCardsDealtToPlayer = (playerIndex: number) => {
    if (dealingPhase === "dealFour") {
      // For dealFour, each player gets up to 4 cards
      return Math.min(
        Math.max(0, Math.floor((cardsDealt - playerIndex) / 4) + (cardsDealt % 4 > playerIndex ? 1 : 0)),
        4
      );
    } else {
      // For dealTwo, each player gets up to 2 cards
      return Math.min(
        Math.max(0, Math.floor((cardsDealt - playerIndex) / 4) + (cardsDealt % 4 > playerIndex ? 1 : 0)),
        2
      );
    }
  };

  return (
    <div className="fixed inset-0 bg-black/90 z-50 flex flex-col items-center justify-center">
      <h2 className="text-2xl font-bold text-[#E1C760] mb-4">
        {dealingPhase === "dealFour" ? "Dealing First 4 Cards" : "Dealing Final 2 Cards"}
      </h2>

      <div className="relative h-80 w-80 mb-8">
        {/* Deck in the center */}
        <div
          className="absolute w-20 h-28 bg-white rounded-md shadow-lg border-2 border-[#E1C760]"
          style={{
            backgroundImage: "url('/CardFaces/card-back.svg')",
            backgroundSize: "cover",
            backgroundPosition: "center",
            left: "calc(50% - 40px)",
            top: "calc(50% - 56px)",
            zIndex: 1
          }}
        />

        {/* Dealing animation */}
        <AnimatePresence>
          {isAnimating && cardsDealt < totalCardsToDeal && (
            <motion.div
              key={`deal-card-${cardsDealt}`}
              className="absolute w-20 h-28 bg-white rounded-md shadow-lg border-2 border-[#E1C760]"
              style={{
                backgroundImage: "url('/CardFaces/card-back.svg')",
                backgroundSize: "cover",
                backgroundPosition: "center",
                left: "calc(50% - 40px)",
                top: "calc(50% - 56px)",
                zIndex: 10
              }}
              initial={{ x: 0, y: 0, rotate: 0, opacity: 1 }}
              animate={{
                x: currentPlayerIndex === 0 ? 0 :
                   currentPlayerIndex === 1 ? 120 :
                   currentPlayerIndex === 2 ? 0 :
                   -120,
                y: currentPlayerIndex === 0 ? -120 :
                   currentPlayerIndex === 1 ? 0 :
                   currentPlayerIndex === 2 ? 120 :
                   0,
                rotate: [0, Math.random() * 10 - 5],
                opacity: [1, 1, 0]
              }}
              transition={{
                duration: 0.4,
                ease: "easeInOut"
              }}
            />
          )}
        </AnimatePresence>

        {/* Player positions with card stacks */}
        {/* Top player */}
        <div className="absolute top-0 left-1/2 transform -translate-x-1/2 flex flex-col items-center">
          <div className="bg-blue-900 text-white px-3 py-1 rounded-full text-sm mb-2">
            {getPlayerName(0)}
          </div>
          <div className="relative w-20 h-28">
            {Array.from({ length: getCardsDealtToPlayer(0) }).map((_, index) => (
              <motion.div
                key={`top-card-${index}`}
                className="absolute w-20 h-28 bg-white rounded-md shadow-lg border-2 border-[#E1C760]"
                style={{
                  backgroundImage: "url('/CardFaces/card-back.svg')",
                  backgroundSize: "cover",
                  backgroundPosition: "center",
                  top: `${index * 2}px`,
                  left: `${index * 2}px`,
                  zIndex: index
                }}
                initial={{ opacity: 0, scale: 0.8 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ duration: 0.2 }}
              />
            ))}
            {getCardsDealtToPlayer(0) === 0 && (
              <div className="w-full h-full border border-gray-600 rounded-md flex items-center justify-center bg-black/30">
                <span className="text-gray-400 text-xs">Waiting...</span>
              </div>
            )}
          </div>
          <div className="mt-1 text-white text-xs">
            {getCardsDealtToPlayer(0)}/{dealingPhase === "dealFour" ? 4 : 2}
          </div>
        </div>

        {/* Right player */}
        <div className="absolute right-0 top-1/2 transform translate-x-1/2 -translate-y-1/2 flex flex-row-reverse items-center">
          <div className="bg-red-900 text-white px-3 py-1 rounded-full text-sm ml-2">
            {getPlayerName(1)}
          </div>
          <div className="relative w-20 h-28">
            {Array.from({ length: getCardsDealtToPlayer(1) }).map((_, index) => (
              <motion.div
                key={`right-card-${index}`}
                className="absolute w-20 h-28 bg-white rounded-md shadow-lg border-2 border-[#E1C760]"
                style={{
                  backgroundImage: "url('/CardFaces/card-back.svg')",
                  backgroundSize: "cover",
                  backgroundPosition: "center",
                  top: `${index * 2}px`,
                  left: `${index * 2}px`,
                  zIndex: index
                }}
                initial={{ opacity: 0, scale: 0.8 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ duration: 0.2 }}
              />
            ))}
            {getCardsDealtToPlayer(1) === 0 && (
              <div className="w-full h-full border border-gray-600 rounded-md flex items-center justify-center bg-black/30">
                <span className="text-gray-400 text-xs">Waiting...</span>
              </div>
            )}
          </div>
          <div className="mr-1 text-white text-xs">
            {getCardsDealtToPlayer(1)}/{dealingPhase === "dealFour" ? 4 : 2}
          </div>
        </div>

        {/* Bottom player */}
        <div className="absolute bottom-0 left-1/2 transform -translate-x-1/2 flex flex-col-reverse items-center">
          <div className="bg-green-900 text-white px-3 py-1 rounded-full text-sm mt-2">
            {getPlayerName(2)}
          </div>
          <div className="relative w-20 h-28">
            {Array.from({ length: getCardsDealtToPlayer(2) }).map((_, index) => (
              <motion.div
                key={`bottom-card-${index}`}
                className="absolute w-20 h-28 bg-white rounded-md shadow-lg border-2 border-[#E1C760]"
                style={{
                  backgroundImage: "url('/CardFaces/card-back.svg')",
                  backgroundSize: "cover",
                  backgroundPosition: "center",
                  top: `${index * 2}px`,
                  left: `${index * 2}px`,
                  zIndex: index
                }}
                initial={{ opacity: 0, scale: 0.8 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ duration: 0.2 }}
              />
            ))}
            {getCardsDealtToPlayer(2) === 0 && (
              <div className="w-full h-full border border-gray-600 rounded-md flex items-center justify-center bg-black/30">
                <span className="text-gray-400 text-xs">Waiting...</span>
              </div>
            )}
          </div>
          <div className="mb-1 text-white text-xs">
            {getCardsDealtToPlayer(2)}/{dealingPhase === "dealFour" ? 4 : 2}
          </div>
        </div>

        {/* Left player */}
        <div className="absolute left-0 top-1/2 transform -translate-x-1/2 -translate-y-1/2 flex flex-row items-center">
          <div className="bg-yellow-900 text-white px-3 py-1 rounded-full text-sm mr-2">
            {getPlayerName(3)}
          </div>
          <div className="relative w-20 h-28">
            {Array.from({ length: getCardsDealtToPlayer(3) }).map((_, index) => (
              <motion.div
                key={`left-card-${index}`}
                className="absolute w-20 h-28 bg-white rounded-md shadow-lg border-2 border-[#E1C760]"
                style={{
                  backgroundImage: "url('/CardFaces/card-back.svg')",
                  backgroundSize: "cover",
                  backgroundPosition: "center",
                  top: `${index * 2}px`,
                  left: `${index * 2}px`,
                  zIndex: index
                }}
                initial={{ opacity: 0, scale: 0.8 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ duration: 0.2 }}
              />
            ))}
            {getCardsDealtToPlayer(3) === 0 && (
              <div className="w-full h-full border border-gray-600 rounded-md flex items-center justify-center bg-black/30">
                <span className="text-gray-400 text-xs">Waiting...</span>
              </div>
            )}
          </div>
          <div className="ml-1 text-white text-xs">
            {getCardsDealtToPlayer(3)}/{dealingPhase === "dealFour" ? 4 : 2}
          </div>
        </div>
      </div>

      {/* Status message */}
      <div className="text-center text-white text-lg mt-4">
        {message}
      </div>

      {/* Progress bar */}
      <div className="w-64 h-2 bg-gray-800 rounded-full mt-4 overflow-hidden">
        <motion.div
          className="h-full bg-[#E1C760]"
          initial={{ width: "0%" }}
          animate={{ width: `${(cardsDealt / totalCardsToDeal) * 100}%` }}
          transition={{ duration: 0.3 }}
        />
      </div>
    </div>
  );
}
