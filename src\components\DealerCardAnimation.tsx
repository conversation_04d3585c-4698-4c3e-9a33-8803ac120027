"use client";
import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from "framer-motion";

// Define card types and positions
type Direction = 'right' | 'up' | 'left' | 'down';
type CardState = {
  id: number;
  direction: Direction;
  dealt: boolean;
  x: number;
  y: number;
  rotation: number;
};

interface DealerCardAnimationProps {
  isVisible: boolean;
  onComplete: () => void;
  cardCount: 2 | 4; // Either 2 or 4 cards per direction
}

export default function DealerCardAnimation({ isVisible, onComplete, cardCount }: DealerCardAnimationProps) {
  const [isDealing, setIsDealing] = useState(false);
  const [cards, setCards] = useState<CardState[]>([]);

  // Reset cards when component becomes visible or card count changes
  useEffect(() => {
    if (isVisible) {
      resetCards();
      // Start dealing animation immediately
      dealCards();
    }
  }, [isVisible, cardCount]);

  // Initialize cards in their starting position
  const resetCards = () => {
    const directions: Direction[] = ['right', 'up', 'left', 'down'];
    const newCards: CardState[] = [];

    for (let i = 0; i < cardCount * 4; i++) {
      newCards.push({
        id: i,
        direction: directions[Math.floor(i / cardCount)],
        dealt: false,
        x: 0,
        y: 0,
        rotation: 0
      });
    }

    setCards(newCards);
    setIsDealing(false);
  };

  // Handle the animation of dealing cards
  const dealCards = () => {
    if (isDealing) return;
    setIsDealing(true);

    // Deal each card with a delay
    const dealCard = (index: number) => {
      if (index >= cards.length) {
        // All cards dealt, wait a moment then call onComplete
        setTimeout(() => {
          setIsDealing(false);
          onComplete();
        }, 1000);
        return;
      }

      setTimeout(() => {
        setCards(prevCards => {
          const newCards = [...prevCards];
          const card = newCards[index];

          if (!card) {
            console.error(`Card at index ${index} is undefined`);
            return prevCards;
          }

          // Calculate the final position based on direction
          let x = 0;
          let y = 0;
          let rotation = 0;

          switch (card.direction) {
            case 'right':
              x = 150;
              rotation = Math.random() * 10 - 5;
              break;
            case 'up':
              y = -150;
              rotation = Math.random() * 10 - 5;
              break;
            case 'left':
              x = -150;
              rotation = Math.random() * 10 - 5;
              break;
            case 'down':
              y = 150;
              rotation = Math.random() * 10 - 5;
              break;
            default:
              // Fallback in case direction is undefined
              console.warn(`Card at index ${index} has undefined direction`);
              // Assign a direction based on the index
              const directions: Direction[] = ['right', 'up', 'left', 'down'];
              const fallbackDirection = directions[Math.floor(index / (cardCount || 1)) % 4];

              switch (fallbackDirection) {
                case 'right':
                  x = 150;
                  rotation = Math.random() * 10 - 5;
                  break;
                case 'up':
                  y = -150;
                  rotation = Math.random() * 10 - 5;
                  break;
                case 'left':
                  x = -150;
                  rotation = Math.random() * 10 - 5;
                  break;
                case 'down':
                  y = 150;
                  rotation = Math.random() * 10 - 5;
                  break;
              }
          }

          newCards[index] = {
            ...card,
            dealt: true,
            x,
            y,
            rotation
          };

          return newCards;
        });

        dealCard(index + 1);
      }, 200);
    };

    dealCard(0);
  };

  if (!isVisible) {
    return null;
  }

  return (
    <div className="fixed inset-0 bg-black flex items-center justify-center z-50">
      {/* Full screen background with table texture */}
      <div className="absolute inset-0 bg-gradient-to-br from-green-900 to-green-950"></div>

      {/* Game table overlay */}
      <div className="absolute inset-0 flex items-center justify-center">
        <div className="w-full h-full max-w-4xl max-h-4xl relative">
          {/* Table border */}
          <div className="absolute inset-0 border-8 border-[#E1C760] rounded-3xl opacity-30"></div>

          {/* Dealer position with glow effect */}
          <div className="absolute top-1/2 left-1/2 w-24 h-24 rounded-full bg-[#E1C760] transform -translate-x-1/2 -translate-y-1/2 flex items-center justify-center shadow-lg shadow-yellow-500/50 border-4 border-yellow-300 z-10">
            <span className="text-black font-bold text-xl">YOU</span>
          </div>

          {/* Player positions */}
          <div className="absolute top-[15%] left-1/2 w-20 h-20 rounded-full bg-blue-600 transform -translate-x-1/2 flex items-center justify-center shadow-md shadow-blue-500/30 border-2 border-blue-300">
            <span className="text-white font-bold text-lg">P1</span>
          </div>
          <div className="absolute top-1/2 right-[15%] w-20 h-20 rounded-full bg-green-600 transform -translate-y-1/2 flex items-center justify-center shadow-md shadow-green-500/30 border-2 border-green-300">
            <span className="text-white font-bold text-lg">P2</span>
          </div>
          <div className="absolute bottom-[15%] left-1/2 w-20 h-20 rounded-full bg-red-600 transform -translate-x-1/2 flex items-center justify-center shadow-md shadow-red-500/30 border-2 border-red-300">
            <span className="text-white font-bold text-lg">P3</span>
          </div>
          <div className="absolute top-1/2 left-[15%] w-20 h-20 rounded-full bg-purple-600 transform -translate-y-1/2 flex items-center justify-center shadow-md shadow-purple-500/30 border-2 border-purple-300">
            <span className="text-white font-bold text-lg">P4</span>
          </div>

          {/* Dealing paths visualization - enhanced curved lines */}
          <svg className="absolute inset-0 w-full h-full" viewBox="0 0 1000 1000" xmlns="http://www.w3.org/2000/svg">
            <path
              d="M 500,500 C 500,350 500,200 500,100"
              stroke="rgba(225, 199, 96, 0.4)"
              strokeWidth="4"
              strokeDasharray="10,10"
              fill="none"
            />
            <path
              d="M 500,500 C 650,500 800,500 900,500"
              stroke="rgba(225, 199, 96, 0.4)"
              strokeWidth="4"
              strokeDasharray="10,10"
              fill="none"
            />
            <path
              d="M 500,500 C 500,650 500,800 500,900"
              stroke="rgba(225, 199, 96, 0.4)"
              strokeWidth="4"
              strokeDasharray="10,10"
              fill="none"
            />
            <path
              d="M 500,500 C 350,500 200,500 100,500"
              stroke="rgba(225, 199, 96, 0.4)"
              strokeWidth="4"
              strokeDasharray="10,10"
              fill="none"
            />
          </svg>

          {/* Deck in center */}
          <div className="absolute top-1/2 left-1/2 w-24 h-36 rounded-md shadow-lg border-2 border-[#E1C760] transform -translate-x-1/2 -translate-y-1/2" style={{
            backgroundImage: "url('/assets/CardBack/GR2.svg')",
            backgroundSize: "cover",
            backgroundPosition: "center",
            zIndex: 5
          }}></div>

          {/* Cards being dealt */}
          <AnimatePresence>
            {cards.map(card => (
              <motion.div
                key={card.id}
                className="absolute top-1/2 left-1/2 w-24 h-36 rounded-md shadow-lg border-2 border-[#E1C760]"
                style={{
                  backgroundImage: "url('/assets/CardBack/GR2.svg')",
                  backgroundSize: "cover",
                  backgroundPosition: "center",
                  zIndex: card.dealt ? 20 : 10
                }}
                initial={{
                  x: 0,
                  y: 0,
                  opacity: 0,
                  rotate: 0,
                  scale: 0.9
                }}
                animate={{
                  x: card.dealt ? card.x * 1.5 : 0, // Increased distance for better visibility
                  y: card.dealt ? card.y * 1.5 : 0, // Increased distance for better visibility
                  opacity: 1,
                  rotate: card.dealt ? card.rotation : 0,
                  scale: card.dealt ? 0.9 : 1
                }}
                exit={{ opacity: 0 }}
                transition={{
                  duration: 0.5,
                  ease: "easeOut",
                  delay: 0.1 * (card.id % cardCount) // Stagger the animations within each direction
                }}
              />
            ))}
          </AnimatePresence>
        </div>
      </div>

      {/* Status message */}
      <div className="absolute bottom-20 left-0 right-0 text-center">
        <div className="bg-black/70 mx-auto max-w-md py-4 px-8 rounded-full border-2 border-[#E1C760]">
          <span className="text-[2rem] font-bold text-[#E1C760] leading-tight">
            {isDealing ? `Dealing ${cardCount} cards to each player...` : 'Ready to deal'}
          </span>
        </div>
      </div>
    </div>
  );
}
