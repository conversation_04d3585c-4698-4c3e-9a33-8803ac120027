@echo off
echo ========================================
echo Starting Thunee Video Server (Production)
echo ========================================

set DEPLOY_DIR=C:\inetpub\Thunee-Production
set NODE_ENV=production
set VIDEO_PORT=3002

echo Deployment directory: %DEPLOY_DIR%
echo Environment: %NODE_ENV%
echo Video server port: %VIDEO_PORT%

echo.
echo Checking if deployment directory exists...
if not exist "%DEPLOY_DIR%" (
    echo Error: Deployment directory not found: %DEPLOY_DIR%
    echo Please run deploy-production.bat first
    pause
    exit /b 1
)

echo.
echo Checking if Node.js is installed...
node --version >nul 2>&1
if %ERRORLEVEL% NEQ 0 (
    echo Error: Node.js is not installed or not in PATH
    echo Please install Node.js from https://nodejs.org/
    pause
    exit /b 1
)

echo.
echo Changing to deployment directory...
cd /d "%DEPLOY_DIR%"

echo.
echo Starting video server...
echo Press Ctrl+C to stop the server
echo.
node server/videoServer.js

echo.
echo Video server stopped.
pause
