@echo off
echo ========================================
echo Thunee Server Dependencies Installer
echo ========================================

echo.
echo This script will install the required dependencies for the Thunee server.
echo.

echo Step 1: Checking if server directory exists...
if not exist "server" (
    echo Error: server directory not found!
    echo Please run this script from the root directory of the Thunee project.
    pause
    exit /b 1
)

echo Step 2: Checking if server package.json exists...
if not exist "server\package.json" (
    echo Error: server\package.json not found!
    echo The server directory appears to be incomplete.
    pause
    exit /b 1
)

echo Step 3: Installing server dependencies...
cd server
call npm install

if %ERRORLEVEL% NEQ 0 (
    echo.
    echo Error: Failed to install server dependencies!
    echo Please check your internet connection and try again.
    echo.
    echo If the problem persists, try:
    echo 1. Delete the server\node_modules folder
    echo 2. Run this script again
    echo.
    pause
    exit /b 1
)

echo.
echo ========================================
echo Server dependencies installed successfully!
echo ========================================
echo.
echo You can now run the server with:
echo   cd server
echo   node index.js
echo.
echo Or use the npm script from the root directory:
echo   npm run video-server
echo.
echo The server will run on http://localhost:3001 by default.
echo.
pause
