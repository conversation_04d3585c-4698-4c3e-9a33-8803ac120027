"use client";
import { useState } from "react";
import { useGameStore } from "@/store/gameStore";
import socketService from "@/services/socketService";

export default function DebugControls() {
  const { players, updateGameState } = useGameStore();
  const [isOpen, setIsOpen] = useState(false);

  const handleSetTurn = (playerId: string) => {
    // Update local state
    const currentPlayerId = socketService.getSocketId();
    updateGameState({
      currentTurn: playerId,
      isCurrentTurn: playerId === currentPlayerId
    });
    
    console.log(`DEBUG: Manually set turn to player ${playerId}`);
    console.log(`DEBUG: Is current player's turn: ${playerId === currentPlayerId}`);
  };

  if (!isOpen) {
    return (
      <button 
        onClick={() => setIsOpen(true)}
        className="fixed bottom-4 right-4 bg-red-600 text-white px-2 py-1 rounded-md text-xs z-50"
      >
        Debug
      </button>
    );
  }

  return (
    <div className="fixed bottom-4 right-4 bg-black/90 border border-red-600 p-4 rounded-md z-50 text-white">
      <div className="flex justify-between mb-2">
        <h3 className="text-red-500 font-bold">Debug Controls</h3>
        <button 
          onClick={() => setIsOpen(false)}
          className="text-white hover:text-red-500"
        >
          X
        </button>
      </div>
      
      <div className="mb-4">
        <h4 className="text-sm font-bold mb-1">Set Player Turn:</h4>
        <div className="flex flex-col gap-1">
          {players.map(player => (
            <button
              key={player.id}
              onClick={() => handleSetTurn(player.id)}
              className="bg-red-800 hover:bg-red-700 px-2 py-1 rounded text-xs text-left"
            >
              {player.name} (Team {player.team})
            </button>
          ))}
        </div>
      </div>
      
      <div className="mb-2">
        <h4 className="text-sm font-bold mb-1">Game State:</h4>
        <button
          onClick={() => console.log('Current game state:', useGameStore.getState())}
          className="bg-red-800 hover:bg-red-700 px-2 py-1 rounded text-xs w-full"
        >
          Log Game State
        </button>
      </div>
      
      <div>
        <button
          onClick={() => {
            const currentPlayerId = socketService.getSocketId();
            console.log(`Current socket ID: ${currentPlayerId}`);
            console.log('All players:', players.map(p => `${p.name} (${p.id}) - Team ${p.team}`));
          }}
          className="bg-red-800 hover:bg-red-700 px-2 py-1 rounded text-xs w-full"
        >
          Log Player IDs
        </button>
      </div>
    </div>
  );
}
