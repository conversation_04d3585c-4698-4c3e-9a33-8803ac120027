# PowerShell script to configure IIS for Thunee application
# Run this script as Administrator

param(
    [string]$SitePath = "C:\inetpub\Thunee-Production",
    [string]$SiteName = "Thunee",
    [int]$Port = 96
)

Write-Host "========================================" -ForegroundColor Green
Write-Host "Thunee IIS Configuration Script" -ForegroundColor Green
Write-Host "========================================" -ForegroundColor Green

# Check if running as Administrator
if (-NOT ([Security.Principal.WindowsPrincipal] [Security.Principal.WindowsIdentity]::GetCurrent()).IsInRole([Security.Principal.WindowsBuiltInRole] "Administrator")) {
    Write-Host "This script requires Administrator privileges. Please run as Administrator." -ForegroundColor Red
    exit 1
}

# Import WebAdministration module
Write-Host "Importing WebAdministration module..." -ForegroundColor Yellow
Import-Module WebAdministration -ErrorAction SilentlyContinue

if (-not (Get-Module WebAdministration)) {
    Write-Host "WebAdministration module not available. Installing IIS Management Tools..." -ForegroundColor Yellow
    Enable-WindowsOptionalFeature -Online -FeatureName IIS-ManagementConsole -All
    Enable-WindowsOptionalFeature -Online -FeatureName IIS-IIS6ManagementCompatibility -All
    Import-Module WebAdministration
}

# Enable required IIS features
Write-Host "Enabling required IIS features..." -ForegroundColor Yellow
$features = @(
    "IIS-WebServerRole",
    "IIS-WebServer",
    "IIS-CommonHttpFeatures",
    "IIS-HttpErrors",
    "IIS-HttpLogging",
    "IIS-RequestFiltering",
    "IIS-StaticContent",
    "IIS-DefaultDocument",
    "IIS-DirectoryBrowsing",
    "IIS-ASPNET45",
    "IIS-NetFxExtensibility45",
    "IIS-ISAPIExtensions",
    "IIS-ISAPIFilter",
    "IIS-WebSockets",
    "IIS-ApplicationDevelopment",
    "IIS-NetFxExtensibility",
    "IIS-ISAPIExtensions",
    "IIS-ISAPIFilter"
)

foreach ($feature in $features) {
    try {
        Enable-WindowsOptionalFeature -Online -FeatureName $feature -All -NoRestart
        Write-Host "Enabled feature: $feature" -ForegroundColor Green
    } catch {
        Write-Host "Failed to enable feature: $feature" -ForegroundColor Red
    }
}

# Install IISNode if not already installed
Write-Host "Checking for IISNode..." -ForegroundColor Yellow
$iisNodePath = "${env:ProgramFiles}\iisnode\iisnode.dll"
if (-not (Test-Path $iisNodePath)) {
    Write-Host "IISNode not found. Please download and install IISNode from:" -ForegroundColor Red
    Write-Host "https://github.com/Azure/iisnode/releases" -ForegroundColor Red
    Write-Host "After installing IISNode, run this script again." -ForegroundColor Red
    exit 1
} else {
    Write-Host "IISNode found at: $iisNodePath" -ForegroundColor Green
}

# Create Application Pool
Write-Host "Creating Application Pool: ThuneeAppPool..." -ForegroundColor Yellow
try {
    Remove-WebAppPool -Name "ThuneeAppPool" -ErrorAction SilentlyContinue
    New-WebAppPool -Name "ThuneeAppPool" -Force
    
    # Configure Application Pool
    Set-ItemProperty -Path "IIS:\AppPools\ThuneeAppPool" -Name "processModel.identityType" -Value "ApplicationPoolIdentity"
    Set-ItemProperty -Path "IIS:\AppPools\ThuneeAppPool" -Name "enable32BitAppOnWin64" -Value $false
    Set-ItemProperty -Path "IIS:\AppPools\ThuneeAppPool" -Name "managedRuntimeVersion" -Value ""
    Set-ItemProperty -Path "IIS:\AppPools\ThuneeAppPool" -Name "processModel.idleTimeout" -Value "00:00:00"
    Set-ItemProperty -Path "IIS:\AppPools\ThuneeAppPool" -Name "recycling.periodicRestart.time" -Value "00:00:00"
    
    Write-Host "Application Pool created and configured successfully." -ForegroundColor Green
} catch {
    Write-Host "Error creating Application Pool: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

# Create Website
Write-Host "Creating Website: $SiteName..." -ForegroundColor Yellow
try {
    Remove-Website -Name $SiteName -ErrorAction SilentlyContinue
    New-Website -Name $SiteName -Port $Port -PhysicalPath $SitePath -ApplicationPool "ThuneeAppPool"
    
    Write-Host "Website created successfully." -ForegroundColor Green
} catch {
    Write-Host "Error creating Website: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

# Configure URL Rewrite (if module is available)
Write-Host "Checking for URL Rewrite module..." -ForegroundColor Yellow
$urlRewritePath = "${env:ProgramFiles}\IIS\Microsoft Web Platform Installer\WebPlatformInstaller.exe"
if (-not (Get-WebConfiguration -Filter "system.webServer/rewrite" -PSPath "IIS:\" -ErrorAction SilentlyContinue)) {
    Write-Host "URL Rewrite module not found. Please install it from:" -ForegroundColor Yellow
    Write-Host "https://www.iis.net/downloads/microsoft/url-rewrite" -ForegroundColor Yellow
} else {
    Write-Host "URL Rewrite module found." -ForegroundColor Green
}

# Set permissions
Write-Host "Setting permissions..." -ForegroundColor Yellow
try {
    icacls $SitePath /grant "IIS_IUSRS:(OI)(CI)F" /T
    icacls $SitePath /grant "IUSR:(OI)(CI)R" /T
    icacls $SitePath /grant "Everyone:(OI)(CI)R" /T
    Write-Host "Permissions set successfully." -ForegroundColor Green
} catch {
    Write-Host "Error setting permissions: $($_.Exception.Message)" -ForegroundColor Red
}

# Start Application Pool and Website
Write-Host "Starting Application Pool and Website..." -ForegroundColor Yellow
try {
    Start-WebAppPool -Name "ThuneeAppPool"
    Start-Website -Name $SiteName
    Write-Host "Application Pool and Website started successfully." -ForegroundColor Green
} catch {
    Write-Host "Error starting services: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host "========================================" -ForegroundColor Green
Write-Host "IIS Configuration completed!" -ForegroundColor Green
Write-Host "========================================" -ForegroundColor Green
Write-Host ""
Write-Host "Website URL: http://**************:$Port" -ForegroundColor Cyan
Write-Host "IISNode logs: http://**************:$Port/iisnode/" -ForegroundColor Cyan
Write-Host ""
Write-Host "Next steps:" -ForegroundColor Yellow
Write-Host "1. Ensure Node.js is installed on the server" -ForegroundColor White
Write-Host "2. Test the application in a web browser" -ForegroundColor White
Write-Host "3. Check IISNode logs if there are any issues" -ForegroundColor White
Write-Host "4. For video server, you may need to run it separately on port 3002" -ForegroundColor White
Write-Host ""
