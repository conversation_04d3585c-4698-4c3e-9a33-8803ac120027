@echo off
echo ========================================
echo Thunee Deployment Test Script
echo ========================================

set DEPLOY_DIR=C:\inetpub\Thunee-Production
set SITE_URL=http://**************:96

echo.
echo Testing deployment at: %DEPLOY_DIR%
echo Website URL: %SITE_URL%

echo.
echo Step 1: Checking deployment directory...
if not exist "%DEPLOY_DIR%" (
    echo ERROR: Deployment directory not found: %DEPLOY_DIR%
    echo Please run deploy-production.bat first
    goto :error
) else (
    echo OK: Deployment directory exists
)

echo.
echo Step 2: Checking required files...
if not exist "%DEPLOY_DIR%\web.config" (
    echo ERROR: web.config not found
    goto :error
) else (
    echo OK: web.config found
)

if not exist "%DEPLOY_DIR%\server\index.js" (
    echo ERROR: server/index.js not found
    goto :error
) else (
    echo OK: server/index.js found
)

if not exist "%DEPLOY_DIR%\dist\index.html" (
    echo ERROR: dist/index.html not found
    goto :error
) else (
    echo OK: dist/index.html found
)

echo.
echo Step 3: Checking Node.js installation...
node --version >nul 2>&1
if %ERRORLEVEL% NEQ 0 (
    echo ERROR: Node.js is not installed or not in PATH
    echo Please install Node.js from https://nodejs.org/
    goto :error
) else (
    for /f "tokens=*" %%i in ('node --version') do set NODE_VERSION=%%i
    echo OK: Node.js version %NODE_VERSION% found
)

echo.
echo Step 4: Checking npm dependencies...
cd /d "%DEPLOY_DIR%"
if not exist "node_modules" (
    echo ERROR: node_modules directory not found
    echo Please run 'npm install' in the deployment directory
    goto :error
) else (
    echo OK: node_modules directory found
)

echo.
echo Step 5: Testing Node.js server startup...
echo Starting server for 10 seconds...
timeout /t 2 /nobreak >nul
start /b node server/index.js
timeout /t 10 /nobreak >nul
taskkill /f /im node.exe >nul 2>&1
echo Server test completed

echo.
echo Step 6: Checking IIS configuration...
powershell -Command "Import-Module WebAdministration; if (Get-Website -Name 'Thunee' -ErrorAction SilentlyContinue) { Write-Host 'OK: Thunee website found in IIS' } else { Write-Host 'ERROR: Thunee website not found in IIS' }"

echo.
echo Step 7: Testing website accessibility...
echo Opening website in default browser...
start "" "%SITE_URL%"
echo.
echo Please check if the website loads correctly in your browser.

echo.
echo ========================================
echo Deployment Test Summary
echo ========================================
echo.
echo If all tests passed:
echo 1. Your deployment is ready
echo 2. Website should be accessible at: %SITE_URL%
echo 3. For video features, start the video server with: start-video-server-production.bat
echo.
echo If there were errors:
echo 1. Check the error messages above
echo 2. Refer to DEPLOYMENT.md for troubleshooting
echo 3. Check IISNode logs at: %SITE_URL%/iisnode/
echo.
goto :end

:error
echo.
echo ========================================
echo DEPLOYMENT TEST FAILED
echo ========================================
echo Please fix the errors above and run the test again.
echo.

:end
echo Press any key to exit...
pause >nul
