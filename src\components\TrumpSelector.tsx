"use client";
import { useState, useEffect } from "react";
import socketService from "@/services/socketService";
import { useGameStore } from "@/store/gameStore";
import { getCardImagePath } from "@/utils/cardUtils";

interface TrumpSelectorProps {
  isVisible: boolean;
  onTrumpSelected: () => void;
}

export default function TrumpSelector({ isVisible, onTrumpSelected }: TrumpSelectorProps) {
  const [isSelecting, setIsSelecting] = useState(false);
  const [canSelectTrump, setCanSelectTrump] = useState(false);
  const { hand, initialHand } = useGameStore();

  // Use initialHand if available, otherwise use hand
  const displayHand = initialHand.length > 0 ? initialHand : hand;

  // Listen for the your_turn_to_select_trump event
  useEffect(() => {
    const handleYourTurnToSelectTrump = () => {
      console.log("It's your turn to select trump");
      setCanSelectTrump(true);
    };

    socketService.on("your_turn_to_select_trump", handleYourTurnToSelectTrump);

    return () => {
      socketService.off("your_turn_to_select_trump", handleYourTurnToSelectTrump);
    };
  }, []);

  // Add useEffect for debugging
  useEffect(() => {
    console.log("TrumpSelector rendered with isVisible:", isVisible);
    console.log("Current hand:", displayHand);
    console.log("Can select trump:", canSelectTrump);
  }, [isVisible, displayHand, canSelectTrump]);

  if (!isVisible) {
    return null;
  }

  const handleSelectTrump = async (suit: string) => {
    if (!canSelectTrump && !isSelecting) {
      console.log("Cannot select trump yet - waiting for your turn");
      return;
    }

    try {
      setIsSelecting(true);
      console.log(`Player selected ${suit} as trump`);

      // Send trump selection to server
      await socketService.sendGameAction("select_trump", {
        suit
      });

      // Reset state
      setCanSelectTrump(false);

      // Show a brief animation
      setTimeout(() => {
        setIsSelecting(false);
        onTrumpSelected();
      }, 1000);
    } catch (error) {
      console.error("Error selecting trump:", error);
      setIsSelecting(false);
      // Re-enable selection on error
      setCanSelectTrump(true);
    }
  };

  // Get unique suits from the player's hand
  const uniqueSuits = [...new Set(displayHand.map(card => card.suit))];
  console.log("Unique suits in hand:", uniqueSuits);
  console.log("Player's hand:", displayHand);

  // Ensure each suit has a unique key
  const suitKeys = uniqueSuits.map(suit => ({ suit, key: `suit-${suit}` }));

  return (
    <div className="fixed inset-0 bg-black/90 flex items-center justify-center z-50 p-2 sm:p-4 md:p-6 overflow-y-auto">
      <div className="bg-black border-4 border-[#E1C760] rounded-lg p-3 sm:p-4 md:p-6 w-full max-w-[95vw] sm:max-w-[90vw] md:max-w-[80vw] lg:max-w-[70vw] mx-auto shadow-[0_0_20px_rgba(225,199,96,0.5)]">
        <div className="border-b-2 border-[#E1C760] bg-black px-3 sm:px-6 py-2 sm:py-3 text-center rounded-t-md">
          <span className="text-xl sm:text-2xl md:text-[1.8rem] font-bold text-[#E1C760] leading-tight">
            Select Trump Suit
          </span>
        </div>

        <div className="bg-black rounded-b-md">
          <p className="text-white text-sm sm:text-base md:text-lg mb-2 sm:mb-4move  text-center">Choose a suit to be the trump for this game.</p>

          {/* Display player's cards */}
          <div className="mb-3 sm:mb-4 md:mb-6">
            <h3 className="text-[#E1C760] text-sm sm:text-base md:text-lg font-bold mb-1 sm:mb-2 text-center">Your Cards</h3>
            <div className="flex justify-center gap-1 sm:gap-2 overflow-x-auto pb-2 max-w-full">
              {displayHand.map(card => (
                <div key={card.id} className="w-12 h-16 sm:w-16 sm:h-24 md:w-20 md:h-28 border-2 border-[#E1C760] rounded overflow-hidden flex-shrink-0 shadow-md">
                  <img
                    src={getCardImagePath(card.value, card.suit)}
                    alt={`${card.value} of ${card.suit}`}
                    className="w-full h-full object-contain"
                  />
                </div>
              ))}
            </div>
          </div>

          <div className="flex flex-wrap gap-2 sm:gap-3 md:gap-4 justify-center">
            {suitKeys.map(({ suit, key }) => (
              <button
                key={key}
                onClick={() => handleSelectTrump(suit)}
                disabled={isSelecting || !canSelectTrump}
                className={`w-[4rem] h-[4rem] sm:w-[5rem] sm:h-[5rem] md:w-[6rem] md:h-[6rem] rounded-lg border-2 border-[#E1C760] bg-black px-2 sm:px-3 py-1 sm:py-1.5 text-xs sm:text-sm font-medium transition-colors ${
                  canSelectTrump && !isSelecting
                    ? "hover:bg-[#E1C760]/20 cursor-pointer"
                    : "opacity-50 cursor-not-allowed"
                } flex flex-col items-center justify-center shadow-[0_0_10px_rgba(225,199,96,0.3)]`}
              >
                <div className="w-8 h-8 sm:w-10 sm:h-10 md:w-12 md:h-12 mb-1 sm:mb-2 rounded-full bg-gradient-to-br from-green-700 to-green-900 flex items-center justify-center p-1.5 sm:p-2">
                  <img
                    src={`/SuitFaces/${suit.charAt(0).toUpperCase()}.svg`}
                    alt={suit}
                    className="w-full h-full"
                  />
                </div>
                <span className="text-sm sm:text-base md:text-[1.2rem] font-bold text-[#E1C760] leading-tight capitalize">
                  {suit}
                </span>
              </button>
            ))}
          </div>

          <div className="mt-3 sm:mt-4 md:mt-6 flex justify-center">
            <button
              onClick={() => handleSelectTrump("last_card")}
              disabled={isSelecting || !canSelectTrump}
              className={`w-full rounded-lg border-2 border-[#E1C760] bg-black px-2 sm:px-3 py-2 sm:py-3 text-base sm:text-lg font-medium text-[#E1C760] transition-colors ${
                canSelectTrump && !isSelecting
                  ? "hover:bg-[#E1C760]/20 cursor-pointer"
                  : "opacity-50 cursor-not-allowed"
              } shadow-[0_0_10px_rgba(225,199,96,0.3)]`}
            >
              <span className="text-sm sm:text-base md:text-[1.2rem] font-bold text-[#E1C760] leading-tight">
                Last Card
              </span>
            </button>
          </div>

          {!canSelectTrump && !isSelecting && (
            <div className="mt-2 sm:mt-3 md:mt-4 text-center text-white">
              <p className="text-sm sm:text-base">Waiting for your turn to select trump...</p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
