# Thunee Server Setup Guide

This guide explains how to set up and run the Thunee WebSocket server for both local development and production deployment.

## Quick Fix for "Cannot find module 'express'" Error

If you're getting the error `Cannot find module 'express'`, it means the server dependencies haven't been installed. Here's how to fix it:

### Option 1: Use the automated script (Windows)
```bash
# Run from the root directory of the project
install-server-dependencies.bat
```

### Option 2: Manual installation
```bash
# Navigate to the server directory
cd server

# Install dependencies
npm install
```

## Local Development Setup

### Prerequisites
- Node.js (version 18 or higher)
- npm (comes with Node.js)

### Steps
1. **Install server dependencies:**
   ```bash
   cd server
   npm install
   ```

2. **Run the server:**
   ```bash
   node index.js
   ```
   
   Or from the root directory:
   ```bash
   npm run video-server
   ```

3. **Verify the server is running:**
   - Open your browser and go to `http://localhost:3001`
   - You should see the Thunee WebSocket Server status page

### Default Configuration
- **Port:** 3001 (automatically finds next available port if 3001 is busy)
- **Environment:** Development
- **CORS:** Allows all origins in development

## Production Deployment (IIS Windows Server)

### Prerequisites
- Windows Server with IIS installed
- Node.js installed on the server
- IISNode module installed

### Automated Deployment
1. **Run the deployment script:**
   ```bash
   scripts\deploy-production.bat
   ```

2. **Transfer files to IIS server:**
   - Copy the generated deployment package to `C:\inetpub\Thunee-Production` on your IIS server

3. **Configure IIS (on the server):**
   ```powershell
   # Run as Administrator
   cd C:\inetpub\Thunee-Production
   .\setup-iis.ps1
   ```

4. **Test the deployment:**
   ```bash
   .\test-deployment.bat
   ```

### Manual Production Setup
If you prefer to set up manually:

1. **Install server dependencies on the server:**
   ```bash
   cd C:\inetpub\Thunee-Production\server
   npm install --production
   ```

2. **Configure environment variables:**
   ```bash
   set NODE_ENV=production
   set PORT=80
   ```

3. **Set up IIS application pool and website** (see `scripts\setup-iis.ps1` for details)

## Troubleshooting

### Common Issues

1. **"Cannot find module 'express'" Error:**
   - **Cause:** Server dependencies not installed
   - **Solution:** Run `install-server-dependencies.bat` or manually install with `cd server && npm install`

2. **Port already in use:**
   - **Cause:** Another application is using port 3001
   - **Solution:** The server automatically finds the next available port, or set a custom port with `PORT=3002 node index.js`

3. **CORS errors in production:**
   - **Cause:** Frontend trying to connect to wrong server URL
   - **Solution:** Ensure frontend is configured to connect to the correct production server URL

4. **IIS deployment fails:**
   - **Cause:** Missing IISNode or incorrect permissions
   - **Solution:** Install IISNode and run the setup script as Administrator

### Checking Server Status

1. **Local development:**
   - Visit `http://localhost:3001` to see server status
   - Check console output for any errors

2. **Production:**
   - Visit `http://**************:96` to see server status
   - Check IISNode logs at `http://**************:96/iisnode/`

### Log Files
- **Development:** Console output
- **Production:** IISNode logs in the IIS logs directory

## Server Architecture

The Thunee server consists of:
- **Express.js** web server for HTTP endpoints
- **Socket.IO** for WebSocket communication
- **Game logic handlers** in the `handlers/` directory
- **Utility modules** in the `utils/` directory

### Key Files
- `server/index.js` - Main server file
- `server/package.json` - Server dependencies
- `server/handlers/` - Game event handlers
- `server/utils/` - Utility functions

## Environment Variables

| Variable | Development | Production | Description |
|----------|-------------|------------|-------------|
| NODE_ENV | development | production | Environment mode |
| PORT | 3001 | 80 (or IIS pipe) | Server port |
| VIDEO_PORT | 3002 | 3002 | Video server port |

## Support

If you continue to have issues:
1. Check that Node.js is properly installed
2. Verify all dependencies are installed in the `server/` directory
3. Check firewall settings for the required ports
4. Review the server logs for specific error messages
