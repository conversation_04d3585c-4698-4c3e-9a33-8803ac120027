import { Competition, LeaderboardEntry, LeaderboardFilter } from "@/types/leaderboard";

// Mock data for competitions
const mockCompetitions: Competition[] = [
  {
    id: "comp-001",
    name: "Summer Championship 2025",
    startDate: "2025-06-01T00:00:00Z",
    endDate: "2025-08-31T23:59:59Z",
    prizes: {
      first: "$500",
      second: "$250",
      third: "$100"
    },
    status: "active",
    description: "The biggest Thunee tournament of the summer! Join now to compete for amazing prizes."
  },
  {
    id: "comp-002",
    name: "Weekly Challenge Cup",
    startDate: "2025-05-15T00:00:00Z",
    endDate: "2025-05-22T23:59:59Z",
    prizes: {
      first: "$100",
      second: "$50",
      third: "$25"
    },
    status: "completed",
    description: "Weekly competition with new challenges every week."
  },
  {
    id: "comp-003",
    name: "Beginner's Tournament",
    startDate: "2025-06-15T00:00:00Z",
    endDate: "2025-06-30T23:59:59Z",
    prizes: {
      first: "$200",
      second: "$100",
      third: "$50"
    },
    status: "upcoming",
    description: "Perfect for new players! Only players with less than 10 games can participate."
  },
  {
    id: "comp-004",
    name: "Pro League Season 1",
    startDate: "2025-07-01T00:00:00Z",
    endDate: "2025-09-30T23:59:59Z",
    prizes: {
      first: "$1000",
      second: "$500",
      third: "$250"
    },
    status: "upcoming",
    description: "The most competitive Thunee league for professional players."
  },
  {
    id: "comp-005",
    name: "Community Cup",
    startDate: "2025-05-01T00:00:00Z",
    endDate: "2025-05-14T23:59:59Z",
    prizes: {
      first: "$150",
      second: "$75",
      third: "$25"
    },
    status: "completed",
    description: "A friendly tournament organized by the community."
  }
];

// Generate random leaderboard entries
const generateMockLeaderboard = (competitionId: string, count: number = 100): LeaderboardEntry[] => {
  const entries: LeaderboardEntry[] = [];
  
  for (let i = 0; i < count; i++) {
    const gamesPlayed = Math.floor(Math.random() * 50) + 10;
    const gamesWon = Math.floor(Math.random() * gamesPlayed);
    const winRate = (gamesWon / gamesPlayed) * 100;
    const score = gamesWon * 100 + Math.floor(Math.random() * 500);
    
    entries.push({
      id: `entry-${competitionId}-${i}`,
      playerId: `player-${Math.floor(Math.random() * 1000)}`,
      playerName: `Player ${Math.floor(Math.random() * 1000)}`,
      score,
      rank: i + 1,
      gamesPlayed,
      gamesWon,
      winRate
    });
  }
  
  // Sort by score descending
  entries.sort((a, b) => b.score - a.score);
  
  // Update ranks
  entries.forEach((entry, index) => {
    entry.rank = index + 1;
  });
  
  return entries;
};

// Mock leaderboard data cache
const mockLeaderboardCache: Record<string, LeaderboardEntry[]> = {};

// Service functions
export const leaderboardService = {
  // Get all competitions
  getCompetitions: async (): Promise<Competition[]> => {
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 500));
    return [...mockCompetitions];
  },
  
  // Get a specific competition by ID
  getCompetition: async (id: string): Promise<Competition | null> => {
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 300));
    return mockCompetitions.find(comp => comp.id === id) || null;
  },
  
  // Get leaderboard for a competition with pagination and filters
  getLeaderboard: async (
    competitionId: string,
    page: number = 1,
    itemsPerPage: number = 10,
    filters?: LeaderboardFilter
  ): Promise<{
    entries: LeaderboardEntry[],
    totalItems: number
  }> => {
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 700));
    
    // Generate or retrieve cached leaderboard
    if (!mockLeaderboardCache[competitionId]) {
      mockLeaderboardCache[competitionId] = generateMockLeaderboard(competitionId);
    }
    
    let filteredEntries = [...mockLeaderboardCache[competitionId]];
    
    // Apply filters if provided
    if (filters) {
      if (filters.minGames !== undefined) {
        filteredEntries = filteredEntries.filter(entry => entry.gamesPlayed >= filters.minGames!);
      }
      
      if (filters.sortBy) {
        const direction = filters.sortDirection === 'asc' ? 1 : -1;
        filteredEntries.sort((a, b) => {
          if (filters.sortBy === 'score') {
            return (b.score - a.score) * direction;
          } else if (filters.sortBy === 'winRate') {
            return (b.winRate - a.winRate) * direction;
          } else if (filters.sortBy === 'gamesPlayed') {
            return (b.gamesPlayed - a.gamesPlayed) * direction;
          }
          return 0;
        });
      }
      
      // Update ranks after sorting
      filteredEntries.forEach((entry, index) => {
        entry.rank = index + 1;
      });
    }
    
    const totalItems = filteredEntries.length;
    
    // Apply pagination
    const startIndex = (page - 1) * itemsPerPage;
    const endIndex = startIndex + itemsPerPage;
    const paginatedEntries = filteredEntries.slice(startIndex, endIndex);
    
    return {
      entries: paginatedEntries,
      totalItems
    };
  }
};
