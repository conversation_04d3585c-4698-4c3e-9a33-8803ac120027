"use client";
import { useState, useEffect } from "react";
import { motion, AnimatePresence } from "framer-motion";
// Use public path for GRCard image
const GRCardImage = "/assets/GRCard.png";

type CutType = 'top' | 'middle' | 'bottom';

interface Card {
  id: number;
  originalIndex: number;
}

interface CutAnimationProps {
  isVisible: boolean;
  position: string | null;
  onComplete: () => void;
}

export default function CutAnimation({ isVisible, position, onComplete }: CutAnimationProps) {
  const [animationComplete, setAnimationComplete] = useState(false);
  const [isAnimating, setIsAnimating] = useState(false);
  const [cards, setCards] = useState<Card[]>(() =>
    Array.from({ length: 24 }, (_, i) => ({ id: i, originalIndex: i }))
  );

  // Reset animation state when visibility changes
  useEffect(() => {
    if (isVisible) {
      setAnimationComplete(false);
      setIsAnimating(false);
      resetCards();
      // Start animation after a brief delay
      setTimeout(() => {
        if (position) {
          animateCut(position as CutType);
        }
      }, 100);
    }
  }, [isVisible, position]);

  // Trigger onComplete callback when animation finishes
  useEffect(() => {
    if (isVisible && animationComplete) {
      const timer = setTimeout(() => {
        onComplete();
      }, 500);
      return () => clearTimeout(timer);
    }
  }, [isVisible, animationComplete, onComplete]);

  const resetCards = () => {
    setCards(Array.from({ length: 24 }, (_, i) => ({ id: i, originalIndex: i })));
  };

  const animateCut = async (cutType: CutType) => {
    if (isAnimating) return;

    setIsAnimating(true);

    // Reset to original position first
    resetCards();
    await new Promise(resolve => setTimeout(resolve, 100));

    let newCards: Card[] = [];

    switch (cutType) {
      case 'top':
        // Cut top 6 cards and move to bottom
        const topCut = cards.slice(0, 6);
        const topRemaining = cards.slice(6);
        newCards = [...topRemaining, ...topCut];
        break;

      case 'middle':
        // Cut middle 8 cards (8-15) and move to top
        const beforeMiddle = cards.slice(0, 8);
        const middleCut = cards.slice(8, 16);
        const afterMiddle = cards.slice(16);
        newCards = [...middleCut, ...beforeMiddle, ...afterMiddle];
        break;

      case 'bottom':
        // Cut bottom 5 cards and move to top
        const bottomCut = cards.slice(-5);
        const bottomRemaining = cards.slice(0, -5);
        newCards = [...bottomCut, ...bottomRemaining];
        break;
    }

    // Animate the transition
    await new Promise(resolve => setTimeout(resolve, 500));
    setCards(newCards);

    setTimeout(() => {
      setIsAnimating(false);
      setAnimationComplete(true);
    }, 1000);
  };

  const getCardStyle = (index: number, card: Card) => {
    const baseTransform = `translateY(${index * -2}px) translateX(${index * 0.5}px)`;

    if (!isAnimating) {
      return {
        transform: baseTransform,
        zIndex: 24 - index,
        transition: 'all 0.3s ease-in-out'
      };
    }

    // During animation, add more dramatic movement
    const cutType = position as CutType;
    const animationOffset = cutType === 'top' ?
      (card.originalIndex < 6 ? 200 : 0) :
      cutType === 'middle' ?
      (card.originalIndex >= 8 && card.originalIndex < 16 ? -150 : 0) :
      (card.originalIndex >= 19 ? -180 : 0);

    return {
      transform: `${baseTransform} translateY(${animationOffset}px)`,
      zIndex: 24 - index,
      transition: 'all 0.8s cubic-bezier(0.4, 0, 0.2, 1)'
    };
  };

  if (!isVisible) {
    return null;
  }

  return (
    <div className="fixed inset-0 bg-black/70 flex items-center justify-center z-50">
      <div className="relative w-full max-w-md mx-auto">
        <div className="bg-green-800/20 rounded-lg p-8 flex items-center justify-center">
          <div className="relative">
            <h3 className="text-center text-[#E1C760] font-semibold mb-6 text-lg">
              Card Deck (24 cards) - {position && (position.charAt(0).toUpperCase() + position.slice(1))} Cut
            </h3>

            <div className="relative w-20 h-28 mx-auto">
              {cards.map((card, index) => (
                <div
                  key={card.id}
                  className="absolute w-20 h-28 rounded-lg border-2 border-gray-300 shadow-lg"
                  style={getCardStyle(index, card)}
                >
                  <img
                    src={GRCardImage}
                    alt="Card back"
                    className="w-full h-full object-cover rounded-lg"
                  />
                </div>
              ))}
            </div>

            {/* Cut indicators */}
            {!isAnimating && (
              <div className="mt-6 text-center">
                <div className="inline-flex items-center space-x-2 bg-black/30 px-4 py-2 rounded-lg">
                  <div className="w-3 h-3 bg-[#E1C760] rounded-full"></div>
                  <span className="text-[#E1C760] text-sm">
                    {position === 'top' ? 'Top section moved to bottom' :
                     position === 'middle' ? 'Middle section moved to top' :
                     'Bottom section moved to top'}
                  </span>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
