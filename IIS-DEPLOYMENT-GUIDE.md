# 🚀 Thunee Game - Complete IIS Windows Server Deployment Guide

## 📋 Overview

This guide provides step-by-step instructions for deploying the Thunee card game to an IIS Windows Server. The deployment supports both development (localhost) and production environments with automatic environment detection.

## 🏗️ Architecture

- **Frontend**: React application (built to `dist/` folder)
- **Backend**: Node.js WebSocket server (`server/index.js`)
- **Video Server**: Separate Node.js server (`server/videoServer.js`)
- **Web Server**: IIS with IISNode for Node.js integration

## 📋 Prerequisites

### On IIS Windows Server:
1. **Windows Server** with IIS installed
2. **Node.js** (v18 or higher) - [Download](https://nodejs.org/)
3. **IISNode** - [Download](https://github.com/Azure/iisnode/releases)
4. **URL Rewrite Module** - [Download](https://www.iis.net/downloads/microsoft/url-rewrite)
5. **Administrator access** to the server

### On Local Development Machine:
1. **Node.js** (v18 or higher)
2. **Git** (if cloning repository)
3. **Access to transfer files** to IIS server (RDP, network share, etc.)

## 🔧 Environment Configuration

The application automatically detects the environment:

### Development Environment:
- **Detection**: `window.location.hostname === 'localhost'`
- **WebSocket Server**: `http://localhost:3001` (configurable)
- **Video Server**: `http://localhost:3002` (configurable)
- **CORS**: Allows all origins (`*`)

### Production Environment:
- **Detection**: Any hostname other than localhost
- **WebSocket Server**: Same as frontend URL (`http://**************:96`)
- **Video Server**: `http://**************:3002`
- **CORS**: Restricted to production domain

## 📦 Deployment Process

### Phase 1: Local Machine Preparation

#### Step 1: Prepare the Application
```bash
# Navigate to your project directory
cd path/to/Thunee-FE

# Ensure dependencies are installed
npm install
```

#### Step 2: Create Deployment Package
```batch
# Run the deployment script
deploy-production.bat
```

**What this script does:**
- Installs/updates npm dependencies
- Builds React frontend with production settings
- Creates deployment directory: `C:\inetpub\Thunee-Production`
- Copies all necessary files
- Installs production dependencies
- Sets up configuration files
- Creates environment variables

#### Step 3: Prepare Files for Transfer
After running the deployment script, gather these files for transfer:

**📁 Main Deployment Package:**
- `C:\inetpub\Thunee-Production\` (entire folder)

**📄 Setup Scripts:**
- `setup-iis.ps1`
- `test-deployment.bat`
- `start-video-server-production.bat`
- `IIS-DEPLOYMENT-GUIDE.md` (this file)

### Phase 2: File Transfer to IIS Server

#### Option A: Remote Desktop Copy
1. Connect to IIS server via Remote Desktop
2. Copy the `C:\inetpub\Thunee-Production\` folder from local to server
3. Copy setup scripts to the server (preferably to the same directory)

#### Option B: ZIP Transfer
1. Create ZIP file containing:
   - `Thunee-Production` folder
   - All setup scripts
2. Transfer ZIP to IIS server via RDP, FTP, or network share
3. Extract on the IIS server

#### Option C: Network Share
1. Set up network share on IIS server
2. Copy files through the network share
3. Ensure proper permissions are maintained

### Phase 3: IIS Server Configuration

#### Step 1: Verify Prerequisites
Connect to your IIS server and verify installations:

```powershell
# Check Node.js installation
node --version
# Should display v18.x.x or higher

# Check if IIS is running
iisreset /?
# Should display IIS reset options

# Check PowerShell execution policy
Get-ExecutionPolicy
# If restricted, run: Set-ExecutionPolicy RemoteSigned -Scope CurrentUser
```

#### Step 2: Navigate to Deployment Directory
```powershell
# Open PowerShell as Administrator
cd C:\inetpub\Thunee-Production
```

#### Step 3: Run IIS Configuration
```powershell
# Configure IIS, create application pool and website
.\setup-iis.ps1
```

**What this script does:**
- Enables required IIS features (WebSockets, URL Rewrite, etc.)
- Creates "ThuneeAppPool" application pool
- Creates "Thunee" website on port 96
- Configures proper permissions
- Sets up IISNode integration

#### Step 4: Test Deployment
```powershell
# Verify deployment is working
.\test-deployment.bat
```

**What this script tests:**
- Deployment directory structure
- Required files presence
- Node.js availability
- npm dependencies
- IIS configuration
- Website accessibility

#### Step 5: Start Video Server (Optional)
```powershell
# Start video calling server
.\start-video-server-production.bat
```

## 🌐 Final Configuration

### Website URLs:
- **Main Application**: `http://**************:96`
- **Video Server**: `http://**************:3002`
- **IISNode Logs**: `http://**************:96/iisnode/`

### File Structure on IIS Server:
```
C:\inetpub\Thunee-Production\
├── dist/                    # Built React frontend
│   ├── index.html          # Main HTML file
│   ├── assets/             # CSS, JS, images
│   ├── CardFace/           # Card images
│   └── SuitFaces/          # Suit images
├── server/                  # Node.js backend
│   ├── index.js            # Main WebSocket server
│   ├── videoServer.js      # Video calling server
│   ├── handlers/           # Game logic handlers
│   ├── utils/              # Utility functions
│   └── iisnode.yml         # IISNode configuration
├── node_modules/           # Production dependencies
├── web.config              # IIS URL rewriting rules
├── package.json            # Production package.json
├── .env                    # Environment variables
└── iisnode/                # IISNode logs (created automatically)
```

## 🔍 Verification Steps

### Step 1: Local Server Test
```powershell
# Test on the server itself
# Open browser and navigate to:
http://localhost:96
```

### Step 2: External Access Test
```bash
# From any external computer, navigate to:
http://**************:96
```

### Step 3: WebSocket Test
- Create a game lobby
- Join with another player
- Verify real-time communication works

### Step 4: Video Server Test (if enabled)
- Start a game with video calling
- Verify video connections work on port 3002

## 🚨 Troubleshooting

### Common Issues and Solutions

#### 1. Website Returns 500 Internal Server Error
**Causes:**
- Node.js not installed
- IISNode not installed
- Incorrect file permissions

**Solutions:**
```powershell
# Check IISNode logs
# Navigate to: http://**************:96/iisnode/

# Verify Node.js installation
node --version

# Reset IIS
iisreset

# Check file permissions
icacls C:\inetpub\Thunee-Production /grant "IIS_IUSRS:(OI)(CI)F" /T
```

#### 2. WebSocket Connection Failed
**Causes:**
- WebSocket feature not enabled
- Firewall blocking connections
- CORS configuration issues

**Solutions:**
```powershell
# Enable WebSocket feature
Enable-WindowsOptionalFeature -Online -FeatureName IIS-WebSockets -All

# Check Windows Firewall
# Ensure ports 96 and 3002 are open

# Verify CORS settings in server code
# Check server/index.js for correct origin configuration
```

#### 3. Static Files Not Loading
**Causes:**
- Incorrect web.config configuration
- Missing URL Rewrite module
- File path issues

**Solutions:**
```powershell
# Verify URL Rewrite module is installed
# Check IIS Manager > Server > Modules

# Verify web.config exists and is properly formatted
# Check C:\inetpub\Thunee-Production\web.config

# Test static file access directly
# Navigate to: http://**************:96/dist/index.html
```

#### 4. Video Server Not Working
**Causes:**
- Port 3002 not available
- Video server not started
- Firewall blocking port 3002

**Solutions:**
```powershell
# Check if port is in use
netstat -an | findstr :3002

# Start video server manually
cd C:\inetpub\Thunee-Production
node server/videoServer.js

# Check Windows Firewall for port 3002
```

### Log Locations

#### IISNode Logs:
- **URL**: `http://**************:96/iisnode/`
- **File Path**: `C:\inetpub\Thunee-Production\iisnode\`

#### Windows Event Logs:
- **Application Log**: Windows Event Viewer > Application
- **System Log**: Windows Event Viewer > System

#### IIS Logs:
- **Default Location**: `C:\inetpub\logs\LogFiles\W3SVC1\`

## 🔄 Updating the Application

### For Code Changes:

#### Step 1: On Local Machine
```batch
# Make your code changes
# Run deployment script again
deploy-production.bat
```

#### Step 2: Transfer Updated Files
- Copy updated `C:\inetpub\Thunee-Production\` folder to server
- Overwrite existing files

#### Step 3: On IIS Server
```powershell
# IIS will automatically restart the application
# No additional steps needed unless major configuration changes
```

### For Configuration Changes:
If you modify IIS settings, you may need to:
```powershell
# Restart IIS
iisreset

# Or restart just the application pool
Restart-WebAppPool -Name "ThuneeAppPool"
```

## 🔒 Security Considerations

### Firewall Configuration:
- **Port 96**: Open for HTTP traffic
- **Port 3002**: Open for video server (if used)
- **Port 443**: Open for HTTPS (if SSL is configured)

### File Permissions:
- **IIS_IUSRS**: Full control over application directory
- **IUSR**: Read access to application directory
- **Application Pool Identity**: Full control over application directory

### CORS Configuration:
- **Development**: Allows all origins (`*`)
- **Production**: Restricted to `http://**************:96` and `https://**************:96`

## 📞 Support and Maintenance

### Regular Maintenance:
1. **Monitor IISNode logs** for errors
2. **Check Windows Event Viewer** for system issues
3. **Monitor server performance** (CPU, memory, disk)
4. **Keep Node.js updated** to latest LTS version
5. **Update dependencies** regularly for security

### Performance Optimization:
1. **Enable compression** in IIS
2. **Configure caching** for static files
3. **Monitor WebSocket connections**
4. **Optimize database queries** (if applicable)

### Backup Strategy:
1. **Regular backups** of `C:\inetpub\Thunee-Production\`
2. **IIS configuration backup** using IIS Manager
3. **Database backups** (if applicable)

## ✅ Success Checklist

- [ ] Node.js installed and working
- [ ] IISNode installed and configured
- [ ] URL Rewrite module installed
- [ ] Website accessible at `http://**************:96`
- [ ] WebSocket connections working (can create/join lobbies)
- [ ] Static files loading correctly
- [ ] No errors in IISNode logs
- [ ] Video server running (if needed) on port 3002
- [ ] Firewall configured properly
- [ ] Application pool running without errors

---

## 📧 Additional Resources

- **IISNode Documentation**: [GitHub](https://github.com/Azure/iisnode)
- **Node.js Downloads**: [nodejs.org](https://nodejs.org/)
- **IIS URL Rewrite**: [IIS.net](https://www.iis.net/downloads/microsoft/url-rewrite)
- **Socket.IO Documentation**: [socket.io](https://socket.io/docs/)

---

*This deployment guide ensures your Thunee card game runs smoothly on IIS Windows Server with proper environment detection and configuration.*
