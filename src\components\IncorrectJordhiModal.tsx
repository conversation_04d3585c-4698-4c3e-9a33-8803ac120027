"use client";
import { useState, useEffect } from "react";
import { useGameStore } from "@/store/gameStore";
import { But<PERSON> } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { motion, AnimatePresence } from "framer-motion";
import { X } from "lucide-react";
import socketService from "@/services/socketService";

export default function IncorrectJordhiModal() {
  const {
    jordhiCalls,
    incorrectJordhiModalOpen,
    updateGameState,
    currentPlayerId,
    players,
    teamNames,
    biddingTeam
  } = useGameStore();

  // Get the current player's team
  const currentPlayer = players.find(p => p.id === currentPlayerId);
  const currentPlayerTeam = currentPlayer?.team;

  // We already have biddingTeam from the destructuring above

  // If current player team is undefined, determine team based on bidding team
  // This is a fallback in case the player's team isn't properly set
  let oppositeTeam: 1 | 2;

  if (currentPlayerTeam === undefined) {
    // If we can't determine the current player's team, use the bidding team
    // to determine which team's <PERSON><PERSON><PERSON> calls to show
    oppositeTeam = biddingTeam === 1 ? 2 : 1;
    console.log('Current player team is undefined, using bidding team to determine opposite team:', oppositeTeam);
  } else {
    // Normal case - show the opposite team's calls
    oppositeTeam = currentPlayerTeam === 1 ? 2 : 1;
  }

  // If we have no Jordhi calls or can't determine teams properly, show all calls
  let oppositeTeamJordhiCalls = jordhiCalls;

  // Only filter if we have calls and can determine teams
  if (jordhiCalls.length > 0 && oppositeTeam !== undefined) {
    // Try to filter Jordhi calls to only show those from the opposite team
    oppositeTeamJordhiCalls = jordhiCalls.filter(call => {
      // Try all possible team property names
      const callTeam =
        typeof call.playerTeam === 'number' ? call.playerTeam :
        typeof call.team === 'number' ? call.team :
        null; // Can't determine team

      console.log(`Call by ${call.playerName}: determined team = ${callTeam}, opposite team = ${oppositeTeam}`);

      // If we can't determine the team, include the call anyway
      if (callTeam === null) return true;

      return callTeam === oppositeTeam;
    });

    // If filtering resulted in no calls, show all calls
    if (oppositeTeamJordhiCalls.length === 0) {
      console.log('No calls from opposite team found, showing all calls');
      oppositeTeamJordhiCalls = jordhiCalls;
    }
  }

  console.log('Current player team:', currentPlayerTeam);
  console.log('Bidding team:', biddingTeam);
  console.log('Opposite team:', oppositeTeam);
  console.log('All Jordhi calls:', jordhiCalls);

  // Log detailed information about each Jordhi call
  jordhiCalls.forEach((call, index) => {
    console.log(`Jordhi call ${index + 1}:`, {
      playerName: call.playerName,
      playerId: call.playerId,
      playerTeam: call.playerTeam,
      team: call.team, // Check if this property exists
      value: call.value,
      handNumber: call.handNumber,
      isFullyValid: call.isFullyValid
    });
  });

  console.log('Filtered Jordhi calls:', oppositeTeamJordhiCalls);

  // Function to get team name
  const getTeamName = (teamNumber: 1 | 2) => {
    return teamNames[teamNumber] || `Team ${teamNumber}`;
  };

  // Function to format suit name
  const formatSuit = (suit: string | null | undefined) => {
    if (!suit) return "Unknown";
    return suit.charAt(0).toUpperCase() + suit.slice(1);
  };

  // Function to get card value name
  const getCardValueName = (value: string) => {
    switch (value) {
      case "9": return "Nine";
      case "10": return "Ten";
      case "J": return "Jack";
      case "Q": return "Queen";
      case "K": return "King";
      case "A": return "Ace";
      default: return value;
    }
  };

  // Handle 4-ball action
  const handle4Ball = (jordhiCall: any) => {
    console.log(`4-balling Jordhi call by ${jordhiCall.playerName}`);

    // Close the modal immediately to prevent multiple clicks
    updateGameState({ incorrectJordhiModalOpen: false });

    // Send the action with a slight delay to ensure the modal is closed first
    setTimeout(() => {
      socketService.sendGameAction("four_eight_ball_selection", {
        ballType: "4 ball",
        option: "Called incorrect Jodhi",
        jordhiCallPlayerId: jordhiCall.playerId
      })
      .then(() => {
        console.log("4-ball action sent successfully");
      })
      .catch(err => {
        console.error("Error sending 4-ball action:", err);
      });
    }, 100);
  };

  return (
    <AnimatePresence>
      {incorrectJordhiModalOpen && (
        <motion.div
          initial={{ opacity: 0, y: 50 }}
          animate={{ opacity: 1, y: 0 }}
          exit={{ opacity: 0, y: 50 }}
          className="fixed inset-0 z-50 flex items-center justify-center p-4"
        >
          <div
            className="absolute inset-0 bg-black/50"
            onClick={() => updateGameState({ incorrectJordhiModalOpen: false })}
          />
          <Card className="relative w-full max-w-md max-h-[80vh] overflow-auto bg-black border-2 border-[#E1C760] p-4 z-10">
            <div className="flex justify-between items-center mb-4">
              <h2 className="text-xl font-bold text-[#E1C760]">4-Ball Opponent's Jordhi Calls</h2>
              <Button
                variant="ghost"
                size="icon"
                onClick={() => updateGameState({ incorrectJordhiModalOpen: false })}
                className="text-[#E1C760] hover:text-white hover:bg-gray-800"
              >
                <X className="h-5 w-5" />
              </Button>
            </div>

            <p className="text-gray-400 text-center mb-4 text-sm italic">
              Select any Jordhi call to 4-ball it. You can 4-ball any Jordhi call made by the opposing team.
              {oppositeTeamJordhiCalls.length === 0 ? (
                <span className="block mt-1 text-[#E1C760]">No Jordhi calls available to 4-ball</span>
              ) : oppositeTeam === 1 ? (
                <span className="block mt-1 text-[#E1C760]">Showing Team 1's Jordhi calls</span>
              ) : (
                <span className="block mt-1 text-[#E1C760]">Showing Team 2's Jordhi calls</span>
              )}
            </p>
            {oppositeTeamJordhiCalls.length === 0 ? (
              <p className="text-gray-400 text-center py-8">No Jordhi calls available to 4-ball at this time.</p>
            ) : (
              <div className="space-y-4">
                {oppositeTeamJordhiCalls.map((call, index) => {
                  return (
                    <div
                      key={index}
                      className="border rounded-md p-3 border-gray-700 bg-gray-900/30"
                    >
                      <div className="flex justify-between items-center mb-2">
                        <h3 className="text-[#E1C760] font-semibold">
                          {call.playerName} ({getTeamName(call.playerTeam)})
                        </h3>
                        <div className="flex items-center">
                          <span className="text-lg font-bold">{call.value}</span>
                        </div>
                      </div>

                      <div className="text-sm mb-3">
                        {/* Show hand number information */}
                        <p className="text-gray-400 mt-1">
                          {call.handNumber > 0
                            ? `Called after trick #${call.handNumber}`
                            : call.handNumber === 0
                              ? "Called before any tricks were won"
                              : ""}
                        </p>
                      </div>

                      <Button
                        onClick={() => handle4Ball(call)}
                        className="w-full bg-red-900 hover:bg-red-800 text-white border border-[#E1C760]"
                      >
                        4-Ball This Jordhi Call
                      </Button>
                    </div>
                  );
                })}
              </div>
            )}
          </Card>
        </motion.div>
      )}
    </AnimatePresence>
  );
}
