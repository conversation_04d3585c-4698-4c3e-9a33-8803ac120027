import { useState, useEffect } from 'react';
import { Settings, Save, RefreshCw } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import socketService from '@/services/socketService';

export default function ServerPortConfig() {
  const [isOpen, setIsOpen] = useState(false);
  const [port, setPort] = useState(localStorage.getItem('thunee_server_port') || '3001');
  const [isTesting, setIsTesting] = useState(false);
  const [testResult, setTestResult] = useState<'success' | 'error' | null>(null);

  // Reset test result when port changes
  useEffect(() => {
    setTestResult(null);
  }, [port]);

  const handleSave = () => {
    socketService.updateServerUrl(port);
    setIsOpen(false);
  };

  const testConnection = async () => {
    setIsTesting(true);
    setTestResult(null);
    
    try {
      // Try to connect with the new port
      await socketService.connect('test-connection');
      setTestResult('success');
      // Disconnect after successful test
      socketService.disconnect();
    } catch (error) {
      console.error('Connection test failed:', error);
      setTestResult('error');
    } finally {
      setIsTesting(false);
    }
  };

  if (!isOpen) {
    return (
      <Button 
        variant="ghost" 
        size="icon" 
        className="absolute top-4 right-4 text-gray-400 hover:text-[#E1C760] hover:bg-transparent"
        onClick={() => setIsOpen(true)}
      >
        <Settings size={20} />
      </Button>
    );
  }

  return (
    <div className="absolute top-0 right-0 p-4 z-50 bg-black/90 border border-[#E1C760] rounded-bl-lg shadow-lg">
      <div className="flex flex-col gap-3">
        <div className="flex justify-between items-center">
          <h3 className="text-[#E1C760] text-sm font-semibold">Server Configuration</h3>
          <Button 
            variant="ghost" 
            size="icon" 
            className="text-gray-400 hover:text-[#E1C760] hover:bg-transparent"
            onClick={() => setIsOpen(false)}
          >
            <span className="sr-only">Close</span>
            &times;
          </Button>
        </div>
        
        <div className="flex gap-2 items-center">
          <span className="text-xs text-gray-300">http://localhost:</span>
          <Input
            value={port}
            onChange={(e) => setPort(e.target.value)}
            className="w-20 h-8 bg-transparent border border-gray-600 text-white text-sm"
          />
        </div>
        
        <div className="flex gap-2">
          <Button 
            size="sm" 
            variant="outline" 
            className="text-xs h-8 bg-transparent border-[#E1C760] text-[#E1C760] hover:bg-[#E1C760]/20"
            onClick={testConnection}
            disabled={isTesting}
          >
            {isTesting ? (
              <RefreshCw size={14} className="mr-1 animate-spin" />
            ) : (
              <RefreshCw size={14} className="mr-1" />
            )}
            Test
          </Button>
          
          <Button 
            size="sm" 
            variant="outline" 
            className="text-xs h-8 bg-transparent border-[#E1C760] text-[#E1C760] hover:bg-[#E1C760]/20"
            onClick={handleSave}
          >
            <Save size={14} className="mr-1" />
            Save
          </Button>
        </div>
        
        {testResult === 'success' && (
          <p className="text-xs text-green-500">Connection successful!</p>
        )}
        
        {testResult === 'error' && (
          <p className="text-xs text-red-500">Connection failed. Make sure the server is running on this port.</p>
        )}
      </div>
    </div>
  );
}
