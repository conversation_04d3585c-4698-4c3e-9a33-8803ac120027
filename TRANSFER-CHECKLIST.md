# 📋 Remote IIS Server Transfer Checklist

## 🖥️ **Local Machine Tasks**

### ✅ Step 1: Create Deployment Package
```batch
deploy-production.bat
```

### ✅ Step 2: Prepare Files for Transfer
After running the deployment script, gather these files:

**📁 Main Deployment Folder:**
- `C:\inetpub\Thunee-Production\` (entire folder)

**📄 Setup Scripts:**
- `setup-iis.ps1`
- `test-deployment.bat`
- `start-video-server-production.bat`
- `DEPLOYMENT.md`
- `TRANSFER-CHECKLIST.md` (this file)

### ✅ Step 3: Package for Transfer
**Option A: ZIP Method**
1. Create a ZIP file containing:
   - `Thunee-Production` folder
   - All setup scripts
2. Transfer ZIP to IIS server
3. Extract on server

**Option B: Direct Copy**
1. Use Remote Desktop file copy
2. Copy folder and scripts directly

---

## 🌐 **IIS Server Tasks (via Remote Desktop)**

### ✅ Step 1: Prerequisites Check
```batch
# Check Node.js installation
node --version

# Should show version 18+ (if not installed, download from nodejs.org)
```

### ✅ Step 2: Verify File Transfer
Ensure these exist on the IIS server:
- `C:\inetpub\Thunee-Production\` folder
- `setup-iis.ps1`
- `test-deployment.bat`
- `start-video-server-production.bat`

### ✅ Step 3: Run Setup (as Administrator)
```powershell
# Open PowerShell as Administrator
# Navigate to where you placed the scripts

# Configure IIS
.\setup-iis.ps1

# Test deployment
.\test-deployment.bat

# Start video server (optional)
.\start-video-server-production.bat
```

---

## 🔍 **Verification Steps**

### ✅ Check Website
1. Open browser on IIS server
2. Navigate to: `http://localhost:96`
3. Should see Thunee game interface

### ✅ Check External Access
1. From another computer
2. Navigate to: `http://**************:96`
3. Should see Thunee game interface

### ✅ Check Logs (if issues)
1. Navigate to: `http://**************:96/iisnode/`
2. Check for error messages

---

## 🚨 **Troubleshooting**

### If Website Doesn't Load:
1. Check IIS Manager - ensure site is started
2. Check Windows Firewall - ensure port 96 is open
3. Check IISNode logs for errors
4. Verify Node.js is installed

### If WebSocket Doesn't Connect:
1. Ensure WebSocket feature is enabled in IIS
2. Check CORS settings in server code
3. Verify no proxy/firewall blocking WebSocket

### If Video Server Doesn't Work:
1. Ensure port 3002 is open in firewall
2. Run video server manually: `node server/videoServer.js`
3. Check if port 3002 is available

---

## 📞 **Quick Reference**

### URLs After Deployment:
- **Main Game**: `http://**************:96`
- **Video Server**: `http://**************:3002`
- **IIS Logs**: `http://**************:96/iisnode/`

### Important Paths:
- **Deployment**: `C:\inetpub\Thunee-Production\`
- **IIS Logs**: `C:\inetpub\Thunee-Production\iisnode\`
- **Node Modules**: `C:\inetpub\Thunee-Production\node_modules\`

### Key Commands:
```batch
# Restart IIS
iisreset

# Check Node.js
node --version

# Test server manually
cd C:\inetpub\Thunee-Production
node server/index.js
```

---

## ✅ **Success Indicators**

- [ ] Website loads at `http://**************:96`
- [ ] Can create/join game lobbies
- [ ] WebSocket connections work
- [ ] No errors in IISNode logs
- [ ] Video server running (if needed)

---

## 🔄 **For Future Updates**

1. Run `deploy-production.bat` on local machine
2. Transfer only the updated files to IIS server
3. IIS will automatically restart the application
4. No need to reconfigure IIS unless major changes
