import { useState, useEffect } from 'react';
import type { Card as CardType } from '../types';
import '../styles/Card.css';

// Use public paths for card faces
const cardFaceMap = {
  '6H': '/CardFace/6H.svg',
  '6D': '/CardFace/6D.svg',
  '6C': '/CardFace/6C.svg',
  '6S': '/CardFace/6S.svg',
};

const CardBack = '/CardBack/GR2.svg';

interface CardProps {
  card: CardType;
  animationDuration?: number;
  team: 1 | 2;
  ballNumber: number;
}

const Card: React.FC<CardProps> = ({ card, animationDuration = 500, team, ballNumber }) => {
  const [isAnimating, setIsAnimating] = useState(false);
  // We don't need to track previous position anymore

  // Initialize animation state when component mounts
  useEffect(() => {
    // Initial setup - no animation needed
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  useEffect(() => {
    // Always update position and trigger animation when card.position changes
    setIsAnimating(true);
    const timer = setTimeout(() => {
      setIsAnimating(false);
    }, animationDuration);

    return () => clearTimeout(timer);
  }, [card.position, animationDuration]);

  const cardStyle = {
    transform: `translate(${card.position.x}px, ${card.position.y}px) rotate(${card.position.rotation}deg)`,
    zIndex: card.position.zIndex,
    transition: isAnimating ? `transform ${animationDuration}ms ease-in-out` : 'none',
  };

  // Determine which card face to show based on the team and card type
  const cardFaceSrc = cardFaceMap[card.face];

  // Determine which cards to show based on team and ball number
  let shouldShowCard = false;

  if (ballNumber === 0) {
    // For ball score 0
    shouldShowCard =
      (team === 1 && (card.face === '6C' || card.face === '6S')) || // 6S is card back for Team 1
      (team === 2 && (card.face === '6D' || card.face === '6H')); // 6H is card back for Team 2
  } else if (ballNumber >= 1 && ballNumber <= 6) {
    // For balls 1-6
    shouldShowCard =
      (team === 1 && (card.face === '6C' || card.face === '6S')) || // 6S is card back for Team 1
      (team === 2 && (card.face === '6D' || card.face === '6H')); // 6H is card back for Team 2
  } else {
    // For balls 7-12
    shouldShowCard =
      (team === 1 && (card.face === '6C' || card.face === '6H')) || // 6H is card back for Team 1
      (team === 2 && (card.face === '6D' || card.face === '6S')); // 6S is card back for Team 2
  }

  // Only show the card if it matches the team's cards
  if (!shouldShowCard) {
    return null;
  }

  // For balls 1-6:
  // - Team 1: 6C (fixed) and CardBack (moving)
  // - Team 2: 6D (fixed) and CardBack (moving)
  // For balls 7-12:
  // - Team 1: 6C (fixed) and 6H as moving card
  // - Team 2: 6D (fixed) and 6S as moving card

  // Get the appropriate card face for the back side
  const getCardBackFace = () => {
    // For ball score 0 or balls 1-6, use the custom card back
    if (ballNumber === 0 || (ballNumber >= 1 && ballNumber <= 6)) {
      // Use the custom card back image
      return CardBack;
    } else {
      // For balls 7-12, use the actual card faces
      if (team === 1) {
        return cardFaceMap['6H']; // 6 of Hearts for Team 1
      } else {
        return cardFaceMap['6S']; // 6 of Spades for Team 2
      }
    }
  };

  // Determine if this is the moving card (not the fixed card)
  const isMovingCard =
    (team === 1 && ((ballNumber === 0 || ballNumber <= 6) ? card.face === '6S' : card.face === '6H')) ||
    (team === 2 && ((ballNumber === 0 || ballNumber <= 6) ? card.face === '6H' : card.face === '6S'));

  return (
    <div className={`card ${card.isFlipped ? 'flipped' : ''}`} style={cardStyle}>
      <div className="card-inner">
        <div className="card-front">
          {/* For ball score 0 or balls 1-6, if it's the moving card, show the card back on the front */}
          {(ballNumber === 0 || ballNumber <= 6) && isMovingCard ? (
            <img src={CardBack} alt="Card Back" />
          ) : (
            <img src={cardFaceSrc} alt={card.face} />
          )}
        </div>
        <div className="card-back">
          <img src={getCardBackFace()} alt="Card Back" />
        </div>
      </div>
    </div>
  );
};

export default Card;
