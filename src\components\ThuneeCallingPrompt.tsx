import { useState, useEffect } from "react";
import socketService from "@/services/socketService";
import { useGameStore } from "@/store/gameStore";

interface ThuneeCallingPromptProps {
  onComplete: () => void;
  duration: number;
  playerRole: "trumper" | "first-remaining" | "last-remaining";
}

export default function ThuneeCallingPrompt({
  onComplete,
  duration,
  playerRole
}: ThuneeCallingPromptProps) {
  const [countdown, setCountdown] = useState(duration);
  const [isProcessing, setIsProcessing] = useState(false);

  // Start countdown timer
  useEffect(() => {
    // Don't start timer if already processing
    if (isProcessing) return;

    const timer = setInterval(() => {
      setCountdown((prev) => {
        if (prev <= 1) {
          clearInterval(timer);
          // When countdown reaches 0, automatically pass
          console.log(`Timeout for ${playerRole} - automatically passing`);
          // Use a flag to prevent multiple calls
          if (!isProcessing) {
            handleTimeoutPass();
          }
          return 0;
        }
        return prev - 1;
      });
    }, 1000);

    return () => clearInterval(timer);
  }, [playerRole, isProcessing]);

  // Separate function for timeout pass to avoid circular references
  const handleTimeoutPass = async () => {
    if (isProcessing) return;

    try {
      setIsProcessing(true);
      console.log(`Timeout - Passed on Thunee opportunity (role: ${playerRole})`);

      // Send the pass action to the server
      await socketService.sendGameAction("pass_thunee", {
        playerRole,
        stage: playerRole,
        reason: "timeout"
      });

      // The server will handle notifying other players if this was the trumper

      // Complete the prompt
      onComplete();
    } catch (error) {
      console.error("Error passing on Thunee:", error);
      setIsProcessing(false);
      // Force complete on error to prevent stuck UI
      onComplete();
    }
  };

  const handleCallThunee = async () => {
    if (isProcessing) return;

    try {
      setIsProcessing(true);
      console.log(`Thunee called (role: ${playerRole})`);

      // Send the Thunee call to the server
      await socketService.sendGameAction("call_thunee", {
        playerRole,
        stage: playerRole
      });

      // Complete the prompt
      onComplete();
    } catch (error) {
      console.error("Error calling Thunee:", error);
      setIsProcessing(false);
      // Force complete on error to prevent stuck UI
      onComplete();
    }
  };

  const handleCallRoyalThunee = async () => {
    if (isProcessing) return;

    try {
      setIsProcessing(true);
      console.log(`Royal Thunee called (role: ${playerRole})`);

      // Send the Royal Thunee call to the server
      await socketService.sendGameAction("call_royal_thunee", {
        playerRole,
        stage: playerRole
      });

      // Complete the prompt
      onComplete();
    } catch (error) {
      console.error("Error calling Royal Thunee:", error);
      setIsProcessing(false);
      // Force complete on error to prevent stuck UI
      onComplete();
    }
  };

  const handlePass = async () => {
    if (isProcessing) return;

    try {
      setIsProcessing(true);
      console.log(`Passed on Thunee opportunity (role: ${playerRole})`);

      // Send the pass action to the server
      await socketService.sendGameAction("pass_thunee", {
        playerRole,
        stage: playerRole,
        reason: "manual"
      });

      // The server will handle notifying other players if this was the trumper

      // Complete the prompt
      onComplete();
    } catch (error) {
      console.error("Error passing on Thunee:", error);
      setIsProcessing(false);
      // Force complete on error to prevent stuck UI
      onComplete();
    }
  };

  // Get role-specific title and message
  const getRoleSpecificContent = () => {
    switch (playerRole) {
      case "trumper":
        return {
          title: "Trumper's Thunee Opportunity",
          message: "As the Trumper, you have first preference to call Thunee."
        };
      case "first-remaining":
        return {
          title: "Thunee Opportunity",
          message: "The Trumper passed. You can now call Thunee."
        };
      case "last-remaining":
        return {
          title: "Last Chance for Thunee",
          message: "This is the last opportunity to call Thunee."
        };
      default:
        return {
          title: "Thunee Opportunity",
          message: "You can call Thunee now."
        };
    }
  };

  const { title, message } = getRoleSpecificContent();

  return (
    <div className="fixed inset-0 bg-black/80 flex items-center justify-center z-50">
      <div className="bg-black border-2 border-[#E1C760] rounded-lg p-6  w-full text-center shadow-[0_0_10px_rgba(225,199,96,0.5)]">
        <h2 className="text-[#E1C760] text-2xl font-bold mb-4">{title}</h2>
        <p className="text-white mb-6">
          {message} You have {countdown} seconds to decide.
        </p>

        <div className="grid grid-cols-1 gap-4 mb-4">
          <button
            onClick={handleCallThunee}
            disabled={isProcessing}
            className="h-12 bg-black text-[#E1C760] text-lg font-medium rounded-lg border-2 border-[#E1C760] hover:bg-[#E1C760]/10 shadow-lg shadow-black/50"
          >
            Call Thunee
          </button>
          {/* <button
            onClick={handleCallRoyalThunee}
            disabled={isProcessing}
            className="h-12 bg-black text-[#E1C760] text-lg font-medium rounded-lg border-2 border-[#E1C760] hover:bg-[#E1C760]/10 shadow-lg shadow-black/50"
          >
            Royal Thunee
          </button> */}
        </div>

        <button
          onClick={handlePass}
          disabled={isProcessing}
          className="w-full h-12 bg-black/50 text-white/80 text-lg font-medium rounded-lg border border-white/30 hover:bg-black/70"
        >
          Pass
        </button>

        <div className="mt-6 text-[#E1C760] text-4xl font-bold">{countdown}</div>
      </div>
    </div>
  );
}
